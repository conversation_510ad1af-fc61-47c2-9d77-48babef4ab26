#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的队列模式调试程序
测试队列模式是否能正确执行简单的移动指令
"""

import sys
import os
import time
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def test_simple_queue():
    """测试简单的队列模式"""
    print("=" * 60)
    print("简单队列模式调试测试")
    print("=" * 60)
    
    socket_fd = -1
    try:
        # 1. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 2. 上电
        print("🔌 正在上电...")
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败，错误码: {result}")
            return
        print("✅ 上电成功")
        time.sleep(2)

        # 3. 获取当前位置
        print("📍 获取当前位置...")
        pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 1, pos)  # 1=直角坐标系
        if result != 0 or len(pos) < 6:
            print(f"❌ 获取位置失败，错误码: {result}")
            return
        
        current_pos = [pos[i] for i in range(6)]
        print(f"   当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
        print(f"   当前姿态: RX={math.degrees(current_pos[3]):.3f}°, RY={math.degrees(current_pos[4]):.3f}°, RZ={math.degrees(current_pos[5]):.3f}°")

        # 4. 设置远程模式
        print("🔧 设置远程模式...")
        result = nrc.set_current_mode(socket_fd, 1)  # 1 = 远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return
        print("✅ 远程模式设置成功")
        time.sleep(0.5)

        # 4.5 检查并设置坐标系
        print("🎯 检查用户坐标系状态...")

        # 获取用户坐标系参数
        user_coord_pos = nrc.VectorDouble()
        result = nrc.get_user_coord_para(socket_fd, 1, user_coord_pos)
        if result == 0 and len(user_coord_pos) >= 6:
            print(f"   用户坐标系1参数: {[user_coord_pos[i] for i in range(6)]}")
        else:
            print(f"   ⚠️ 获取用户坐标系参数失败，错误码: {result}")

        # 设置用户坐标系编号
        result = nrc.set_user_coord_number(socket_fd, 1)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        print("✅ 用户坐标系编号设置成功")

        # 尝试设置当前坐标系为直角坐标系
        print("🔧 设置当前坐标系为直角坐标系...")
        result = nrc.set_current_coord(socket_fd, 1)  # 1=直角坐标系
        if result != 0:
            print(f"❌ 设置直角坐标系失败，错误码: {result}")
        else:
            print("✅ 直角坐标系设置成功")

        time.sleep(0.2)

        # 5. 启动队列模式
        print("🚀 启动队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启动队列模式失败，错误码: {result}")
            return
        print("✅ 队列模式启动成功")
        time.sleep(0.5)

        # 6. 清除队列数据
        print("🧹 清除队列数据...")
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return
        print("✅ 队列数据清除成功")

        # 7. 创建一个简单的移动指令（相对当前位置小幅移动）
        print("📝 创建移动指令...")
        
        # 目标位置：在当前位置基础上，Z轴向上移动10mm
        target_pos = current_pos.copy()
        target_pos[2] += 10.0  # Z轴向上10mm
        
        print(f"   目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        print(f"   目标姿态: RX={math.degrees(target_pos[3]):.3f}°, RY={math.degrees(target_pos[4]):.3f}°, RZ={math.degrees(target_pos[5]):.3f}°")

        # 8. 添加指令到队列
        print("➕ 添加指令到队列...")
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 1  # 笛卡尔坐标
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)
        
        move_cmd.coord = 1  # 直角坐标系（测试用）
        # move_cmd.userNum = 1  # 直角坐标系不需要userNum
        move_cmd.velocity = 100
        move_cmd.acc = 30
        move_cmd.dec = 30
        move_cmd.pl = 0
        
        print(f"   🔍 指令详情:")
        print(f"      targetPosType: {move_cmd.targetPosType}")
        print(f"      targetPosValue: {list(move_cmd.targetPosValue)}")
        print(f"      coord: {move_cmd.coord}")
        print(f"      velocity: {move_cmd.velocity}")
        
        result = nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 添加指令失败，错误码: {result}")
            return
        print("✅ 指令添加成功")

        # 9. 发送队列
        print("📤 发送队列...")
        result = nrc.queue_motion_send_to_controller(socket_fd, 1)  # 队列大小为1
        if result != 0:
            print(f"❌ 发送队列失败，错误码: {result}")
            return
        print("✅ 队列发送成功")

        # 10. 等待执行完成
        print("⏳ 等待执行完成...")

        # 监控队列执行状态
        start_time = time.time()
        while time.time() - start_time < 30:  # 最多等待30秒
            try:
                # 检查机器人运行状态
                running_status = 0
                result = nrc.get_robot_running_state(socket_fd, running_status)
                if isinstance(result, list) and len(result) > 1:
                    running_status = result[1]

                # 检查队列长度
                queue_len = 0
                result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
                if isinstance(result, list) and len(result) > 1:
                    queue_len = result[1]

                print(f"   运行状态: {running_status}, 队列长度: {queue_len}")

                # 如果机器人停止运行且队列为空，说明执行完成
                if running_status == 0 and queue_len == 0:
                    print("   ✅ 队列执行完成")
                    break

                time.sleep(0.5)

            except Exception as e:
                print(f"   ⚠️ 检查状态时发生错误: {e}")
                time.sleep(0.5)
        else:
            print("   ⚠️ 等待超时")

        # 11. 检查最终位置
        print("📍 检查最终位置...")
        final_pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 1, final_pos)
        if result == 0 and len(final_pos) >= 6:
            final_position = [final_pos[i] for i in range(6)]
            print(f"   最终位置: X={final_position[0]:.3f}, Y={final_position[1]:.3f}, Z={final_position[2]:.3f}")
            print(f"   最终姿态: RX={math.degrees(final_position[3]):.3f}°, RY={math.degrees(final_position[4]):.3f}°, RZ={math.degrees(final_position[5]):.3f}°")
            
            # 检查Z轴是否移动了
            z_movement = final_position[2] - current_pos[2]
            print(f"   Z轴移动量: {z_movement:.3f}mm (预期: 10.0mm)")
            
            if abs(z_movement - 10.0) < 1.0:
                print("✅ 队列模式工作正常！")
            else:
                print("❌ 队列模式可能有问题，移动量不符合预期")

        # 12. 关闭队列模式
        print("🔒 关闭队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, False)
        if result != 0:
            print(f"⚠️ 关闭队列模式失败，错误码: {result}")
        else:
            print("✅ 队列模式已关闭")

        # 13. 对比测试：使用普通robot_movel
        print("\n" + "=" * 40)
        print("🔄 对比测试：使用普通robot_movel")
        print("=" * 40)

        # 获取当前位置作为新的起点
        current_pos2 = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 1, current_pos2)
        if result == 0 and len(current_pos2) >= 6:
            current_position2 = [current_pos2[i] for i in range(6)]
            print(f"   当前位置: X={current_position2[0]:.3f}, Y={current_position2[1]:.3f}, Z={current_position2[2]:.3f}")

            # 目标位置：Z轴再向上移动10mm
            target_pos2 = current_position2.copy()
            target_pos2[2] += 10.0

            print(f"   目标位置: X={target_pos2[0]:.3f}, Y={target_pos2[1]:.3f}, Z={target_pos2[2]:.3f}")

            # 创建普通运动指令
            move_cmd2 = nrc.MoveCmd()
            move_cmd2.targetPosType = 1
            move_cmd2.targetPosValue = nrc.VectorDouble()
            for val in target_pos2:
                move_cmd2.targetPosValue.append(val)

            move_cmd2.coord = 1  # 直角坐标系
            # move_cmd2.userNum = 1  # 直角坐标系不需要
            move_cmd2.velocity = 20
            move_cmd2.acc = 30
            move_cmd2.dec = 30
            move_cmd2.pl = 0

            print("   📤 发送普通robot_movel指令...")
            result = nrc.robot_movel(socket_fd, move_cmd2)
            if result == 0:
                print("   ✅ 指令发送成功")

                # 等待运动完成
                print("   ⏳ 等待运动完成...")
                start_time = time.time()
                while time.time() - start_time < 30:
                    running_status = 0
                    result = nrc.get_robot_running_state(socket_fd, running_status)
                    if isinstance(result, list) and len(result) > 1:
                        if result[1] == 0:  # 运动完成
                            break
                    time.sleep(0.1)

                # 检查最终位置
                final_pos2 = nrc.VectorDouble()
                result = nrc.get_current_position(socket_fd, 1, final_pos2)
                if result == 0 and len(final_pos2) >= 6:
                    final_position2 = [final_pos2[i] for i in range(6)]
                    print(f"   最终位置: X={final_position2[0]:.3f}, Y={final_position2[1]:.3f}, Z={final_position2[2]:.3f}")

                    z_movement2 = final_position2[2] - current_position2[2]
                    x_movement2 = final_position2[0] - current_position2[0]
                    print(f"   Z轴移动量: {z_movement2:.3f}mm (预期: 10.0mm)")
                    print(f"   X轴移动量: {x_movement2:.3f}mm (预期: 0.0mm)")

                    if abs(z_movement2 - 10.0) < 1.0 and abs(x_movement2) < 1.0:
                        print("   ✅ 普通robot_movel工作正常！")
                        print("   🔍 这证明问题出在队列模式的坐标解释上")
                    else:
                        print("   ❌ 普通robot_movel也有问题")
            else:
                print(f"   ❌ 普通指令发送失败，错误码: {result}")

        # 14. 设置回示教模式
        result = nrc.set_current_mode(socket_fd, 0)  # 0 = 示教模式
        if result != 0:
            print(f"⚠️ 设置示教模式失败，错误码: {result}")
        else:
            print("✅ 已切换回示教模式")

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        if socket_fd > 0:
            # 下电并断开连接
            print("🔌 下电并断开连接...")
            nrc.set_servo_poweroff(socket_fd)
            nrc.disconnect_robot(socket_fd)
            print("✅ 测试完成")

if __name__ == "__main__":
    test_simple_queue()
