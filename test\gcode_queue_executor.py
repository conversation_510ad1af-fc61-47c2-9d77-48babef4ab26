#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (队列模式版本)

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令 (包括X, Y, Z位置和A, B, C角度)。
3. 连接到INEXBOT机械臂。
4. 使用队列模式批量发送运动指令，实现更平滑的连续运动。
5. 在指定的用户坐标系下，将G-code路径点转换为机械臂的运动指令并执行。
6. 任务结束后安全下电并断开连接。

注意: 此版本使用队列模式，支持完整的6轴运动，包括XYZ位置和ABC角度。
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径 (请根据您的项目结构调整)
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc


def normalize_angle_degrees(angle):
    """
    将角度标准化到 [-180, 180] 范围内
    这与机械臂内部的角度标准化保持一致
    """
    while angle > 180:
        angle -= 360
    while angle <= -180:
        angle += 360
    return angle


def get_current_pose_angles(socket_fd):
    """获取机械臂当前姿态角度（度）"""
    try:
        pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 3, pos)  # 用户坐标系（与运动命令一致）
        if result == 0 and len(pos) >= 6:
            # 将弧度转换为度并标准化
            rx_deg = normalize_angle_degrees(math.degrees(pos[3]))
            ry_deg = normalize_angle_degrees(math.degrees(pos[4]))
            rz_deg = normalize_angle_degrees(math.degrees(pos[5]))
            return [rx_deg, ry_deg, rz_deg]
        else:
            print(f"❌ 获取当前姿态失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前姿态时发生错误: {e}")
        return None

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 50  # 速度百分比 (0-100)
ACCEL_PERCENT = 20     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)

# 4. 队列模式参数
QUEUE_BATCH_SIZE = 50  # 每批次发送的指令数量
QUEUE_TIMEOUT = 300    # 队列执行超时时间 (秒)

# 5. G-code默认角度值 (当G-code中没有指定ABC角度时的G-code角度值)
GCODE_DEFAULT_A = 0.0   # G-code中A轴的默认角度 (度)
GCODE_DEFAULT_B = 0.0   # G-code中B轴的默认角度 (度)
GCODE_DEFAULT_C = 0.0   # G-code中C轴的默认角度 (度)

# 6. G-code角度到机械臂角度的偏移量
GCODE_TO_ROBOT_OFFSET_A = 180.0  # A轴偏移量 (度)
GCODE_TO_ROBOT_OFFSET_B = 0.0    # B轴偏移量 (度)
GCODE_TO_ROBOT_OFFSET_C = 0.0    # C轴偏移量 (度)

# 7. 计算出的机械臂默认姿态 (G-code默认角度 + 偏移量)
DEFAULT_RX = GCODE_DEFAULT_A + GCODE_TO_ROBOT_OFFSET_A  # = 0 + 180 = 180°
DEFAULT_RY = GCODE_DEFAULT_B + GCODE_TO_ROBOT_OFFSET_B  # = 0 + 0 = 0°
DEFAULT_RZ = GCODE_DEFAULT_C + GCODE_TO_ROBOT_OFFSET_C  # = 0 + 0 = 0°


def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点。
    返回一个包含XYZ位置和ABC角度的字典列表。
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []

    # 正则表达式用于匹配G0/G1指令中的坐标轴和值（包括XYZABC）
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                # 忽略注释和空行
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))
                    # 只有当至少包含XYZ坐标时才添加路径点
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A')) if 'A' in coords else None,
                            'b': float(coords.get('B')) if 'B' in coords else None,
                            'c': float(coords.get('C')) if 'C' in coords else None
                        }
                        path.append(point)
        
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")
        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


def wait_for_queue_complete(socket_fd, timeout_seconds=300):
    """等待队列中所有运动完成"""
    print("  ⏳ 正在等待队列运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]
            
            if running_status == 0:  # 0表示停止，即队列执行完成
                print(" ✅")
                return True
            
            if time.time() - last_print_time > 3:  # 每3秒打印一次状态
                print(".", end="", flush=True)
                last_print_time = time.time()

            time.sleep(0.5)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(1)

    print(" ❌ 队列执行超时!")
    return False


def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True
        
        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)
        
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False
        
        time.sleep(1.5)
        
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False


def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False


def create_move_command(point, current_angles):
    """根据G-code点创建移动命令"""
    # 处理角度
    gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
    gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
    gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

    # 加上偏移量得到机械臂角度
    rx_deg_raw = gcode_a + GCODE_TO_ROBOT_OFFSET_A
    ry_deg_raw = gcode_b + GCODE_TO_ROBOT_OFFSET_B
    rz_deg_raw = gcode_c + GCODE_TO_ROBOT_OFFSET_C

    # 标准化角度
    rx_deg = normalize_angle_degrees(rx_deg_raw)
    ry_deg = normalize_angle_degrees(ry_deg_raw)
    rz_deg = normalize_angle_degrees(rz_deg_raw)

    # 将角度转换为弧度（API需要弧度）
    rx_rad = math.radians(rx_deg)
    ry_rad = math.radians(ry_deg)
    rz_rad = math.radians(rz_deg)

    # 创建位置向量
    pos = nrc.VectorDouble()
    target_pos = [point['x'], point['y'], point['z'], rx_rad, ry_rad, rz_rad]
    for val in target_pos:
        pos.append(val)

    # 创建运动命令
    cmd = nrc.MoveCmd()
    cmd.targetPosType = 1  # 笛卡尔坐标
    cmd.targetPosValue = pos
    cmd.coord = 3  # 用户坐标系类型
    cmd.userNum = USER_COORD_NUMBER
    cmd.velocity = VELOCITY_PERCENT
    cmd.acc = ACCEL_PERCENT
    cmd.dec = ACCEL_PERCENT
    cmd.pl = SMOOTHING_LEVEL

    return cmd, (rx_deg, ry_deg, rz_deg)


def execute_gcode_with_queue():
    """使用队列模式执行G-code的主函数"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序 (队列模式)")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 5. 显示队列模式配置
        print(f"ℹ️ 队列模式配置:")
        print(f"   批次大小: {QUEUE_BATCH_SIZE} 个指令")
        print(f"   队列超时: {QUEUE_TIMEOUT} 秒")
        print(f"   运动参数: 速度={VELOCITY_PERCENT}%, 加速度={ACCEL_PERCENT}%, 平滑度={SMOOTHING_LEVEL}")

        # 6. 显示角度处理配置
        print(f"ℹ️ G-code默认角度: A={GCODE_DEFAULT_A:.1f}°, B={GCODE_DEFAULT_B:.1f}°, C={GCODE_DEFAULT_C:.1f}°")
        print(f"ℹ️ G-code角度偏移: A+{GCODE_TO_ROBOT_OFFSET_A:.1f}°, B+{GCODE_TO_ROBOT_OFFSET_B:.1f}°, C+{GCODE_TO_ROBOT_OFFSET_C:.1f}°")
        print(f"ℹ️ 机械臂默认姿态: RX={DEFAULT_RX:.1f}°, RY={DEFAULT_RY:.1f}°, RZ={DEFAULT_RZ:.1f}°")

        # 7. 获取初始姿态作为参考
        print("ℹ️ 获取当前机械臂姿态作为角度计算参考...")
        current_angles = get_current_pose_angles(socket_fd)
        if current_angles:
            print(f"   当前姿态: RX={current_angles[0]:.3f}°, RY={current_angles[1]:.3f}°, RZ={current_angles[2]:.3f}°")
        else:
            print("   无法获取当前姿态，将使用默认参考角度")
            current_angles = [DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ]

        # 8. 启用队列模式
        print("\n🔄 启用队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return
        print("✅ 队列模式已启用")

        # 9. 分批处理路径点
        total_points = len(gcode_path)
        batch_count = (total_points + QUEUE_BATCH_SIZE - 1) // QUEUE_BATCH_SIZE
        
        print(f"\n🚀 开始执行 {total_points} 个路径点，分为 {batch_count} 个批次...")
        print("=" * 40)

        for batch_idx in range(batch_count):
            start_idx = batch_idx * QUEUE_BATCH_SIZE
            end_idx = min(start_idx + QUEUE_BATCH_SIZE, total_points)
            batch_points = gcode_path[start_idx:end_idx]
            
            print(f"\n--- 批次 {batch_idx + 1}/{batch_count}: 点 {start_idx + 1}-{end_idx} ---")
            
            # 构建当前批次的队列
            queue_size = 0
            for i, point in enumerate(batch_points):
                global_idx = start_idx + i
                print(f"  📍 添加点 {global_idx + 1}: X={point['x']:.3f}, Y={point['y']:.3f}, Z={point['z']:.3f}")
                
                # 创建移动命令
                cmd, predicted_angles = create_move_command(point, current_angles)
                
                # 显示角度信息
                angle_info = []
                if point['a'] is not None:
                    angle_info.append(f"A={point['a']:.1f}°→RX={predicted_angles[0]:.1f}°")
                else:
                    angle_info.append(f"A=默认→RX={predicted_angles[0]:.1f}°")
                
                if point['b'] is not None:
                    angle_info.append(f"B={point['b']:.1f}°→RY={predicted_angles[1]:.1f}°")
                else:
                    angle_info.append(f"B=默认→RY={predicted_angles[1]:.1f}°")
                
                if point['c'] is not None:
                    angle_info.append(f"C={point['c']:.1f}°→RZ={predicted_angles[2]:.1f}°")
                else:
                    angle_info.append(f"C=默认→RZ={predicted_angles[2]:.1f}°")
                
                print(f"     角度: {', '.join(angle_info)}")
                
                # 添加到队列（使用直线运动）
                result = nrc.queue_motion_push_back_moveL(socket_fd, cmd)
                if result == 0:
                    queue_size += 1
                    current_angles = predicted_angles  # 更新角度参考
                else:
                    print(f"❌ 点 {global_idx + 1} 添加到队列失败，错误码: {result}")
                    break
            
            if queue_size == 0:
                print("❌ 当前批次没有成功添加任何指令到队列")
                break
            
            # 发送当前批次到控制器
            print(f"  📤 发送批次到控制器，队列长度: {queue_size}")
            result = nrc.queue_motion_send_to_controller(socket_fd, queue_size)
            if result != 0:
                print(f"❌ 发送队列到控制器失败，错误码: {result}")
                break
            
            # 等待当前批次执行完成
            print(f"  ⏳ 等待批次 {batch_idx + 1} 执行完成...")
            if not wait_for_queue_complete(socket_fd, QUEUE_TIMEOUT):
                print(f"❌ 批次 {batch_idx + 1} 执行超时")
                break
            
            print(f"  ✅ 批次 {batch_idx + 1} 执行完成")
            
            # 批次间短暂休息
            if batch_idx < batch_count - 1:  # 不是最后一个批次
                time.sleep(0.5)

        # 10. 验证最终位置
        print("\n📍 验证最终到达位置...")
        final_angles = get_current_pose_angles(socket_fd)
        if final_angles:
            print(f"   最终姿态: RX={final_angles[0]:.3f}°, RY={final_angles[1]:.3f}°, RZ={final_angles[2]:.3f}°")
        
        print("\n" + "=" * 40)
        print("🎉 所有G-code路径执行完毕！")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 11. 关闭队列模式并安全下电
        if socket_fd > 0:
            try:
                print("\n🔄 关闭队列模式...")
                nrc.queue_motion_set_status(socket_fd, False)
                time.sleep(0.5)
            except Exception as e:
                print(f"⚠️ 关闭队列模式时发生错误: {e}")
            
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")


if __name__ == "__main__":
    execute_gcode_with_queue()