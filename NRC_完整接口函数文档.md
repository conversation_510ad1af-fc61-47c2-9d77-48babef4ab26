# NRC Python 完整接口函数文档

## 文档说明

本文档基于 `lib\nrc_interface.py` 和 `lib\nrc_host.pyd` 编写，这些库文件是通过SWIG工具从C++代码转换而来的Python库。

**库文件说明：**
- `lib\nrc_host.pyd` - Python扩展模块（Windows平台）
- `lib\nrc_interface.py` - Python接口模块

**C++源接口文档参考：**
- [nrc_interface.h](https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h.html) - 主要接口函数
- [nrc_io.h](https://doc.hmilib.inexbot.coision.cn/nrc__io_8h.html) - I/O操作函数
- [nrc_job_operate.h](https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h.html) - 作业操作函数
- [nrc_modbus.h](https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h.html) - Modbus通信函数
- [nrc_queue_operate.h](https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h.html) - 队列操作函数
- [nrc_track.h](https://doc.hmilib.inexbot.coision.cn/nrc__track_8h.html) - 轨迹跟踪函数

**重要提醒：**
- 对于带有引用(&)输出参数的C++函数，Python中返回列表，实际数据在 `result[1]` 中
- 循环调用时需要重置参数为初始值
- 所有函数都有对应的 `_robot` 版本用于多机器人控制
- **角度单位：机械臂内部使用弧度制，所有角度相关的参数（关节角度、姿态角度RX/RY/RZ等）均为弧度**

## 关键技术说明

### SWIG转换机制
本Python库通过SWIG工具从C++代码自动生成，因此：
1. **引用参数处理**：C++中的引用参数(`&`)在Python中变成返回列表
2. **函数签名变化**：原本的输出参数变成了函数的输入参数，但实际数据通过返回值获取
3. **内存管理**：SWIG自动处理C++对象的内存分配和释放

### 引用参数函数的正确使用模式
```python
# ✅ 正确的使用方式
def get_robot_status_correctly(socket_fd):
    # 1. 初始化输出参数
    status = 0

    # 2. 调用函数
    result = nrc.get_servo_state(socket_fd, status)

    # 3. 检查返回值并获取数据
    if isinstance(result, list) and len(result) > 1:
        error_code = result[0]  # 错误码
        actual_status = result[1]  # 实际数据

        if error_code == 0:  # 成功
            return actual_status
        else:
            print(f"函数调用失败，错误码: {error_code}")
            return None
    else:
        print("返回值格式异常")
        return None

# ✅ 循环调用的正确方式
def monitor_robot_status(socket_fd, duration=10):
    start_time = time.time()
    while time.time() - start_time < duration:
        # 每次循环都重置参数
        status = 0
        result = nrc.get_servo_state(socket_fd, status)

        if isinstance(result, list) and len(result) > 1:
            if result[0] == 0:  # 成功
                print(f"当前状态: {result[1]}")
            else:
                print(f"获取状态失败: {result[0]}")

        time.sleep(0.5)

# ❌ 错误的使用方式
def get_robot_status_incorrectly(socket_fd):
    status = 0
    result = nrc.get_servo_state(socket_fd, status)
    # 错误：直接使用status变量，而不是result[1]
    return status  # 这里的status不是期望的数据！
```

## 核心编程模式对比

NRC接口提供了三种主要的机器人控制模式，每种模式适用于不同的应用场景：

| 编程模式 | 描述 | 适用场景 | 优点 | 缺点 |
|:---|:---|:---|:---|:---|
| **直接指令模式**<br>(第13章 `robot_...`) | PC发送一条指令，机器人执行一条。实时性强，同步阻塞。 | 简单的脚本、遥操作、教学、测试 | 简单直接，即时反馈 | 复杂路径或逻辑需要PC端大量计算和循环，网络延迟会影响平滑性 |
| **作业模式**<br>(第18章 `job_...`) | 在PC端将指令集（包括运动、逻辑、IO）编辑成一个"作业"文件，上传至控制器后，由控制器独立运行 | 机器人需要脱离PC独立、稳定地执行重复性任务，如产线上的搬运、焊接 | 运行稳定、可靠，不受PC或网络状态影响 | 灵活性差，无法实时响应外部动态变化（如视觉系统的动态定位） |
| **队列模式**<br>(第19章 `queue_...`) | PC端实时生成指令并将其推入控制器内部的缓冲区（队列），机器人按顺序从队列中取出并平滑执行 | 视觉引导的动态抓取、复杂路径规划、传送带跟踪等需要PC实时决策但又要求机器人运动平滑的应用 | 结合了直接模式的灵活性和作业模式的运动平滑性，是最高级的控制模式 | 编程逻辑最复杂，需要PC端管理好指令的生成和队列状态 |

### 模式选择建议

- **初学者/简单应用**：使用直接指令模式
- **生产环境/重复任务**：使用作业模式
- **复杂动态应用**：使用队列模式

## 重要说明：角度单位

**⚠️ 关键信息：机械臂内部使用弧度制**

所有涉及角度的参数都使用**弧度**作为单位，包括但不限于：
- 关节角度 (J1, J2, J3, J4, J5, J6)
- 姿态角度 (RX, RY, RZ)
- 工具手姿态偏移 (A, B, C)
- 用户坐标系姿态参数

**角度转换公式：**
```python
import math

# 度转弧度
radians = math.radians(degrees)
# 或者
radians = degrees * math.pi / 180

# 弧度转度
degrees = math.degrees(radians)
# 或者
degrees = radians * 180 / math.pi

# 示例：-90度 = -1.571弧度
angle_deg = -90
angle_rad = math.radians(angle_deg)  # -1.5707963267948966
print(f"{angle_deg}度 = {angle_rad:.3f}弧度")
```

## 常量定义

为提高代码可读性，建议使用以下常量：

```python
# 坐标系类型
COORD_JOINT = 0      # 关节坐标
COORD_CARTESIAN = 1  # 直角坐标
COORD_TOOL = 2       # 工具坐标
COORD_USER = 3       # 用户坐标

# 伺服状态
SERVO_STOP = 0       # 停止
SERVO_READY = 1      # 就绪
SERVO_ALARM = 2      # 报警
SERVO_RUNNING = 3    # 运行

# 运行状态
ROBOT_STOPPED = 0    # 停止
ROBOT_RUNNING = 1    # 运行
ROBOT_DECEL = 2      # 减速

# 模式类型
MODE_TEACH = 0       # 示教模式
MODE_REMOTE = 1      # 远程模式
MODE_RUN = 2         # 运行模式

# 示教类型
TEACH_JOG = 0        # 点动
TEACH_DRAG = 1       # 拖拽
```

## 快速入门

以下是一个最简单的"Hello World"示例，演示基本的连接、上电、运动和下电流程。这个示例可以直接复制运行，帮助您快速验证环境配置：

```python
import sys
import os
import time

sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

import nrc_interface as nrc
import time


def hello_world_robot():
    """
    机器人Hello World示例
    这是一个完整的端到端示例，展示了机器人控制的基本流程
    """
    print("🤖 开始机器人Hello World示例...")
    # 坐标系类型
    COORD_JOINT = 0      # 关节坐标
    COORD_CARTESIAN = 1  # 直角坐标
    COORD_TOOL = 2       # 工具坐标
    COORD_USER = 3       # 用户坐标

    # 伺服状态
    SERVO_STOP = 0       # 停止
    SERVO_READY = 1      # 就绪
    SERVO_ALARM = 2      # 报警
    SERVO_RUNNING = 3    # 运行

    # 运行状态
    ROBOT_STOPPED = 0    # 停止
    ROBOT_RUNNING = 1    # 运行
    ROBOT_DECEL = 2      # 减速

    # 模式类型
    MODE_TEACH = 0       # 示教模式
    MODE_REMOTE = 1      # 远程模式
    MODE_RUN = 2         # 运行模式

    # 示教类型
    TEACH_JOG = 0        # 点动
    TEACH_DRAG = 1       # 拖拽

    # 步骤1: 连接机器人控制器
    print("\n📡 步骤1: 连接机器人...")
    socket_fd = nrc.connect_robot("************", "6001")
    if socket_fd <= 0:
        print("❌ 连接失败，请检查IP地址和网络连接")
        return False

    print("✅ 连接成功！Socket ID:", socket_fd)

    try:
        # 步骤2: 清除可能存在的错误
        print("\n🔧 步骤2: 清除系统错误...")
        nrc.clear_error(socket_fd)
        print("✅ 系统错误已清除")

        # 步骤3: 设置伺服为就绪状态
        print("\n⚡ 步骤3: 设置伺服就绪...")
        nrc.set_servo_state(socket_fd, SERVO_READY)
        time.sleep(0.5)  # 等待状态切换
        print("✅ 伺服已就绪")

        # 步骤4: 机器人上电
        print("\n🔋 步骤4: 机器人上电...")
        nrc.set_servo_poweron(socket_fd)
        time.sleep(2)  # 等待上电完成
        print("✅ 机器人上电成功")

        # 步骤5: 设置运动参数
        print("\n⚙️ 步骤5: 配置运动参数...")
        nrc.set_speed(socket_fd, 30)  # 设置30%的安全速度
        nrc.set_current_coord(socket_fd, COORD_JOINT)  # 使用关节坐标系
        print("✅ 运动参数配置完成")

        # 步骤6: 创建运动命令
        print("\n📋 步骤6: 创建运动命令...")
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0  # 0=关节坐标类型
        move_cmd.targetPosValue = nrc.VectorDouble()

        # 设置目标关节角度 [J1, J2, J3, J4, J5, J6] (单位：弧度)
        joint_angles = [0, 0, 0, 0, 0, 0]  # 一个安全的测试位置
        for angle in joint_angles:
            move_cmd.targetPosValue.append(angle)

        move_cmd.coord = COORD_JOINT
        move_cmd.velocity = 30      # 速度百分比
        move_cmd.acc = 40          # 加速度百分比
        move_cmd.dec = 40          # 减速度百分比
        print("✅ 运动命令创建完成")

        # 步骤7: 执行运动
        print("\n🚀 步骤7: 执行机器人运动...")
        result = nrc.robot_movej(socket_fd, move_cmd)
        if result == 0:
            print("✅ 运动命令发送成功，机器人开始移动...")

            # 步骤8: 监控运动状态直到完成
            print("\n⏳ 步骤8: 等待运动完成...")
            start_time = time.time()
            while True:
                running_status = 0
                res_list = nrc.get_robot_running_state(socket_fd, running_status)
                if isinstance(res_list, list) and len(res_list) > 1:
                    current_status = res_list[1]
                    if current_status == ROBOT_STOPPED:
                        elapsed_time = time.time() - start_time
                        print(f"✅ 运动完成！耗时: {elapsed_time:.2f}秒")
                        break
                    elif current_status == ROBOT_RUNNING:
                        print("🔄 机器人运动中...", end='\r')

                time.sleep(0.1)  # 100ms检查间隔
        else:
            print(f"❌ 运动命令失败，错误代码: {result}")
            return False

        # 步骤9: 安全下电
        print("\n🔌 步骤9: 机器人下电...")
        nrc.set_servo_poweroff(socket_fd)
        print("✅ 机器人已安全下电")

        print("\n🎉 Hello World示例执行成功！")
        return True

    except Exception as e:
        print(f"\n❌ 执行过程中发生异常: {e}")
        # 异常情况下尝试安全停止
        try:
            nrc.robot_stop(socket_fd)
            nrc.set_servo_poweroff(socket_fd)
            print("✅ 已执行紧急停止和下电")
        except:
            print("⚠️ 紧急停止失败，请手动检查机器人状态")
        return False

    finally:
        # 步骤10: 断开连接
        print("\n🔗 步骤10: 断开连接...")
        nrc.disconnect_robot(socket_fd)
        print("✅ 连接已断开")

def main():
    """主函数 - 程序入口点"""
    print("=" * 60)
    print("🤖 NRC Python接口 - Hello World示例")
    print("=" * 60)
    print("📝 这个示例将演示:")
    print("   • 连接机器人控制器")
    print("   • 系统初始化和上电")
    print("   • 执行简单的关节运动")
    print("   • 安全下电和断开连接")
    print("=" * 60)

    # 询问用户是否继续
    user_input = input("\n是否继续执行示例？(y/n): ").lower().strip()
    if user_input not in ['y', 'yes', '是']:
        print("👋 示例已取消")
        return

    # 执行示例
    success = hello_world_robot()

    print("\n" + "=" * 60)
    if success:
        print("🎊 恭喜！您已成功完成第一个机器人控制程序")
        print("💡 接下来您可以:")
        print("   • 修改关节角度尝试不同的位置（注意：角度单位为弧度）")
        print("   • 尝试直线运动(robot_movel)")
        print("   • 探索更多高级功能")
    else:
        print("😞 示例执行失败")
        print("🔍 请检查:")
        print("   • 机器人控制器是否正常运行")
        print("   • IP地址是否正确")
        print("   • 网络连接是否正常")
        print("   • 机器人是否处于可控制状态")
    print("=" * 60)

# 程序入口点
if __name__ == "__main__":
    main()
```

## 1. 库信息和连接管理

### 1.1 库版本信息

#### `get_library_version()`
获取库版本信息。

**返回值：**
- `version` (str): 库版本字符串

**示例：**
```python
version = nrc.get_library_version()
print(f"NRC库版本: {version}")
```

### 1.2 连接管理

#### `connect_robot(ip, port)`
连接机器人控制器。

**参数：**
- `ip` (str): 控制器IP地址
- `port` (str): 端口号

**返回值：**
- `socket_fd` (int): 套接字文件描述符，>0表示成功，≤0表示失败

#### `disconnect_robot(socketFd)`
断开机器人控制器连接。

**参数：**
- `socketFd` (int): 套接字文件描述符

**返回值：**
- `result` (int): 0表示成功

#### `get_connection_status(socketFd)`
获取连接状态。

**参数：**
- `socketFd` (int): 套接字文件描述符

**返回值：**
- `status` (int): 0表示已连接，非零表示断开

#### `set_reconnect(socketFd, reconnect)`
设置是否开启断开后自动重连功能（默认关闭）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `reconnect` (bool): True开启自动重连

## 2. 消息通信

### 2.1 消息发送和接收

#### `send_message(socketFd, messageID, message)`
向控制器发送消息。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `messageID` (int): 消息ID
- `message` (str): 消息内容

#### `recv_message(*args)`
接收控制器消息（回调方式）。

**使用说明：**
- 通常与消息回调函数配合使用
- 需要先设置消息接收回调函数

#### `set_receive_error_or_warning_message_callback(*args)`
设置接收错误或警告信息的回调函数。

**参数：**
- 回调函数格式：`callback(message_type, message, message_code)`

**注意：** 回调函数内不能做耗时操作或阻塞。

**示例：**
```python
def error_callback(message_type, message, message_code):
    print(f"错误类型: {message_type}, 代码: {message_code}, 消息: {message}")

nrc.set_receive_error_or_warning_message_callback(socket_fd, error_callback)
```

## 3. 多机器人控制

### 3.1 并行模式

#### `set_robots_parallel(socketFd, open)`
设置多机器人并行模式。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `open` (bool): True开启并行模式

## 4. 伺服控制

### 4.1 错误处理

#### `clear_error(socketFd)`
清除伺服错误。

**参数：**
- `socketFd` (int): 套接字文件描述符

#### `clear_error_robot(socketFd, robotNum)`
清除指定机器人的伺服错误。

### 4.2 伺服状态控制

#### `set_servo_state(socketFd, state)`
设置伺服状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `state` (int): 0=停止，1=就绪

**注意：**
- 设置就绪前应先确保系统无错误
- 仅在停止(0)或就绪(1)状态时有效
- 报警(2)或运行(3)状态时不能直接设置

#### `get_servo_state(socketFd, status)`
获取伺服状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `status` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为伺服状态
- `result[1]` (int): 0=停止，1=就绪，2=报警，3=运行

**注意：**
- 循环调用时需要重置`status`参数为初始整数值

### 4.3 电源控制

#### `set_servo_poweron(socketFd)`
机器人上电。

**注意：**
- 调用前需先设置伺服为就绪状态(1)
- 上电成功后伺服状态变为运行(3)

#### `set_servo_poweroff(socketFd)`
机器人下电。

**注意：**
- 仅在运行状态(3)时调用有效
- 下电成功后伺服状态变为就绪(1)

## 5. 位置和运动

### 5.1 位置获取

#### `get_current_position(socketFd, coord, pos)`
获取机器人当前位置。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `coord` (int): 坐标系类型（0=关节，1=直角，2=工具，3=用户）
- `pos` (VectorDouble): 位置向量（需预先初始化）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码（0表示成功），`result[1]`为位置数据

**注意：**
- 由于C++接口使用引用参数，Python中返回列表
- 位置数据在`result[1]`中获取
- 循环调用时需要重置`pos`参数

#### `get_current_extra_position(socketFd, pos)`
获取机器人外部轴当前位置。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `pos` (VectorDouble): 位置向量（需预先初始化）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为外部轴位置数据

**注意：**
- 循环调用时需要重置`pos`参数

#### `get_robot_configuration(socketFd, configuration)`
获取4轴SCARA机器人的形态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `configuration` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为机器人形态配置
- `result[1]` (int): 机器人形态配置

**注意：**
- 循环调用时需要重置`configuration`参数为初始整数值

### 5.2 运行状态

#### `get_robot_running_state(socketFd, status)`
获取机器人运行状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `status` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为运行状态
- `result[1]` (int): 0=停止，1=运行，2=减速

**注意：**
- 循环调用时需要重置`status`参数为初始整数值

## 6. 速度和模式控制

### 6.1 速度控制

#### `set_speed(socketFd, speed)`
设置当前模式的速度。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `speed` (int): 速度百分比（0 < speed ≤ 100）

**注意：** 有三种模式：示教模式、运行模式、远程模式

#### `get_speed(socketFd, speed)`
获取当前模式的速度。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `speed` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为速度值
- `result[1]` (int): 当前速度百分比

**注意：**
- 循环调用时需要重置`speed`参数为初始整数值

### 6.2 坐标系控制

#### `set_current_coord(socketFd, coord)`
设置机器人当前坐标系。

**参数：**
- `coord` (int): 坐标系类型（0=关节，1=直角，2=工具，3=用户）

#### `get_current_coord(socketFd, coord)`
获取机器人当前坐标系。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `coord` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为坐标系类型
- `result[1]` (int): 当前坐标系类型

**注意：**
- 循环调用时需要重置`coord`参数为初始整数值

### 6.3 模式控制

#### `set_current_mode(socketFd, mode)`
设置机器人当前模式。

**参数：**
- `mode` (int): 0=示教，1=远程，2=运行

#### `get_current_mode(socketFd, mode)`
获取机器人当前模式。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `mode` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为当前模式
- `result[1]` (int): 当前模式

**注意：**
- 循环调用时需要重置`mode`参数为初始整数值

### 6.4 机器人类型

#### `get_robot_type(socketFd, type)`
获取机器人类型。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `type` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为机器人类型
- `result[1]` (int): 机器人类型

**注意：**
- 循环调用时需要重置`type`参数为初始整数值

### 6.5 示教类型

#### `set_teach_type(socketFd, type)`
设置示教模式类型。

**参数：**
- `type` (int): 0=点动，1=拖拽

#### `get_teach_type(socketFd, type)`
获取示教模式类型。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `type` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为示教类型
- `result[1]` (int): 示教类型

**注意：**
- 循环调用时需要重置`type`参数为初始整数值

## 7. 位置可达性检查

#### `get_pos_reachable(socketFd, pos, movetype, result)`
检查位置是否可达。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `pos` (list): 目标位置
- `movetype` (str): 运动类型（如"movel"）
- `result` (bool): 输出参数

**返回值：**
- `result[1]` (bool): True表示可达，False表示不可达

## 8. 工具手管理

### 8.1 工具手编号

#### `set_tool_hand_number(socketFd, toolNum)`
设置工具手编号。

**参数：**
- `toolNum` (int): 工具手编号

#### `get_tool_hand_number(socketFd, toolNum)`
获取当前使用的工具手编号。

**返回值：**
- `result[1]` (int): 当前工具手编号

### 8.2 工具手参数

#### `set_tool_hand_param(socketFd, toolNum, param)`
设置工具手参数。

**参数：**
- `toolNum` (int): 工具手编号
- `param` (ToolParam): 工具参数对象

**ToolParam结构说明：**
```python
tool_param = nrc.ToolParam()
tool_param.x = 10.0    # X方向位置偏移 (mm)
tool_param.y = 5.0     # Y方向位置偏移 (mm)
tool_param.z = 15.0    # Z方向位置偏移 (mm)
tool_param.a = 0.0     # A轴姿态偏移 (弧度)
tool_param.b = 0.0     # B轴姿态偏移 (弧度)
tool_param.c = 0.0     # C轴姿态偏移 (弧度)
```

#### `get_tool_hand_param(socketFd, toolNum, param)`
获取工具手参数。

### 8.3 工具手标定

#### `tool_hand_2_or_20_point_calibrate(socketFd, point)`
2点或20点标定工具手。

**参数：**
- `point` (int): 标定点编号（1,2）

**注意：** 标定前使用 `set_tool_hand_number` 选择需要标定的工具手

#### `tool_hand_2_or_20_point_calibrate_caculate(socketFd, calNum=1, noCalZero=False)`
2点或20点标定计算。

**参数：**
- `calNum` (int): 计算编号
- `noCalZero` (bool): True=校准工具尺寸+姿态不校准零点，False=校准零点

#### `tool_hand_2_or_20_point_calibrate_clear(socketFd, pointNum)`
清除标定点。

**参数：**
- `pointNum` (int): 清除工具手编号（1-20）

#### `tool_hand_7_point_calibrate(socketFd, point, toolNum)`
7点标定工具手。

**参数：**
- `point` (int): 标定点编号（1-7）
- `toolNum` (int): 工具手编号

#### `tool_hand_7_point_calibrate_caculate(socketFd, toolNum, calibrationPointNum=7)`
7点标定计算。

**参数：**
- `toolNum` (int): 工具手编号
- `calibrationPointNum` (int): 标定点数量

#### `tool_hand_7_point_calibrate_clear(socketFd, pointNum, toolNum)`
清除7点标定点。

**参数：**
- `pointNum` (int): 清除标定点编号（1-7）
- `toolNum` (int): 工具手编号

## 9. 用户坐标系

### 9.1 用户坐标编号

#### `set_user_coord_number(socketFd, userNum)`
设置用户坐标编号。

**参数：**
- `userNum` (int): 用户坐标编号

#### `get_user_coord_number(socketFd, userNum)`
获取当前使用的用户坐标编号。

**返回值：**
- `result[1]` (int): 当前用户坐标编号

### 9.2 用户坐标参数

#### `get_user_coord_para(socketFd, userNum, pos)`
获取用户坐标系参数。

**参数：**
- `userNum` (int): 用户坐标编号
- `pos` (VectorDouble): 输出位置向量

#### `set_user_coordinate_data(socketFd, userNum, pos)`
标定用户坐标。

**参数：**
- `userNum` (int): 用户坐标编号
- `pos` (list): 坐标数据

### 9.3 用户坐标标定

#### `calibration_oxy(socketFd, userNum, xyo)`
标定OXY。

**参数：**
- `userNum` (int): 用户坐标编号
- `xyo` (str): "O"=原点，"X"=X轴点，"Y"=Y轴点

#### `calculate_user_coordinate(socketFd, userNumber)`
计算用户坐标。

**参数：**
- `userNumber` (int): 用户坐标编号

## 10. 全局位置和变量

### 10.1 全局位置

#### `set_global_position(socketFd, posName, posInfo)`
设置全局位置（GP点）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `posName` (str): 位置名称（如"GP0001"）
- `posInfo` (list): 位置信息数组

**posInfo格式：**
```
[坐标系, 角度单位, 配置, 工具号, 用户号, 保留, 保留, X, Y, Z, RX, RY, RZ, 保留]
```
**注意：** RX, RY, RZ 姿态角度使用弧度制

#### `get_global_position(socketFd, posName, pos)`
获取全局位置。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `posName` (str): 位置名称
- `pos` (VectorDouble): 输出位置向量（需预先初始化）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为位置数据

**注意：**
- 循环调用时需要重置`pos`参数

#### `set_global_sync_position(socketFd, posName, posInfo)`
设置全局同步位置。

#### `get_global_sync_position(socketFd, posName, pos)`
获取全局同步位置。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `posName` (str): 位置名称
- `pos` (VectorDouble): 输出位置向量（需预先初始化）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为位置数据

**注意：**
- 循环调用时需要重置`pos`参数

### 10.2 全局变量

#### `set_global_variant(socketFd, varName, varValue)`
设置全局变量。

**参数：**
- `varName` (str): 变量名（如"GI001", "GD001", "GB001"）
- `varValue` (float): 变量值

**变量类型：**
- GI: 整数变量
- GD: 双精度变量
- GB: 布尔变量

#### `get_global_variant(socketFd, varName, value)`
获取全局变量。

**返回值：**
- `result[1]` (float): 变量值

## 11. 轴控制

### 11.1 零点设置

#### `set_axis_zero_position(socketFd, axis)`
设置轴零点位置。

**参数：**
- `axis` (int): 轴编号

#### `set_zero_pos_deviation(socketFd, axis, shift)`
设置零点偏移。

**参数：**
- `axis` (int): 轴编号
- `shift` (float): 偏移量

## 12. 四点标定

### 12.1 四点标定功能

#### `get_single_cycle(socketFd, single_cycle)`
获取单圈值。

**返回值：**
- `result[1]` (int): 单圈值

#### `get_four_point(socketFd, result)`
获取四点标定结果。

#### `set_four_point_mark(socketFd, point, status)`
设置四点标记。

**参数：**
- `point` (int): 点编号
- `status` (bool): 标记状态

#### `four_point_calculation(socketFd, L1, L2, result)`
四点计算。

**参数：**
- `L1` (float): 长度1
- `L2` (float): 长度2
- `result` (VectorDouble): 输出结果

## 13. 运动控制

### 13.1 基本运动

#### `robot_movel(socketFd, moveCmd)`
直线运动。

**参数：**
- `moveCmd` (MoveCmd): 运动命令对象

**MoveCmd结构说明：**
```python
move_cmd = nrc.MoveCmd()
move_cmd.targetPosType = nrc.PosType_data  # 目标位置类型 (0=实际坐标数据, 1=位置变量编号)
move_cmd.targetPosValue = nrc.VectorDouble()  # 目标位置 [X,Y,Z,RX,RY,RZ] (姿态单位：弧度) 或 [J1,J2,J3,J4,J5,J6] (关节单位：弧度)
move_cmd.coord = 3                  # 坐标系 (0=关节, 1=直角, 2=工具, 3=用户)
move_cmd.velocity = 50              # 速度百分比 (1-100)
move_cmd.acc = 40                   # 加速度百分比
move_cmd.dec = 40                   # 减速度百分比
move_cmd.pl = 0                     # 平滑度等级
move_cmd.time = 0                   # 时间限制 (0=无限制)
move_cmd.toolNum = 0                # 工具编号
move_cmd.userNum = 1                # 用户坐标编号
```

#### `robot_movej(socketFd, moveCmd)`
关节运动。

#### `robot_movec(socketFd, moveCmd)`
圆弧运动。

#### `robot_moveca(socketFd, moveCmd)`
圆弧运动（通过角度）。

#### `robot_moves(socketFd, moveCmd)`
样条运动。

#### `robot_imove(socketFd, moveCmd)`
增量运动。

### 13.2 外部轴运动

#### `robot_movel_extra(socketFd, moveCmd)`
外部轴直线运动。

#### `robot_movej_extra(socketFd, moveCmd)`
外部轴关节运动。

### 13.3 伺服运动

#### `servo_move(socketFd, servoMove)`
伺服运动。

**参数：**
- `servoMove` (ServoMove): 伺服运动对象

### 13.4 点动控制

#### `robot_start_jogging(socketFd, axis, direction)`
开始点动。

**参数：**
- `axis` (int): 轴编号
- `direction` (bool): 运动方向

#### `robot_stop_jogging(socketFd)`
停止点动。

### 13.5 运动停止

#### `robot_stop(socketFd)`
停止机器人运动。

#### `robot_stop_not_power_off(socketFd)`
停止机器人运动但不下电。

## 14. 坐标变换

### 14.1 坐标转换

#### `get_origin_coord_to_target_coord(socketFd, originCoord, originPos, targetCoord, targetPos)`
坐标系转换。

**参数：**
- `originCoord` (int): 源坐标系
- `originPos` (VectorDouble): 源位置
- `targetCoord` (int): 目标坐标系
- `targetPos` (VectorDouble): 目标位置（输出）

#### `get_joint_to_cartesian(socketFd, jointPos, cartesianPos)`
关节坐标转直角坐标。

#### `get_cartesian_to_joint(socketFd, cartesianPos, jointPos)`
直角坐标转关节坐标。

## 15. I/O控制

### 15.1 数字I/O

#### `set_digital_output(socketFd, port, value)`
设置数字输出。

**参数：**
- `port` (int): 端口号
- `value` (int): 输出值（0或1）

#### `get_digital_input(socketFd, port, value)`
获取数字输入。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `port` (int): 端口号
- `value` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为输入值
- `result[1]` (int): 输入值

**注意：**
- 循环调用时需要重置`value`参数为初始整数值

#### `get_digital_output(socketFd, port, value)`
获取数字输出状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `port` (int): 端口号
- `value` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为输出状态

### 15.2 模拟I/O

#### `set_analog_output(socketFd, port, value)`
设置模拟输出。

**参数：**
- `port` (int): 端口号
- `value` (float): 输出值

#### `get_analog_input(socketFd, port, value)`
获取模拟输入。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `port` (int): 端口号
- `value` (float): 输出参数（需要初始化为浮点值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为输入值
- `result[1]` (float): 输入值

**注意：**
- 循环调用时需要重置`value`参数为初始浮点值

#### `get_analog_output(socketFd, port, value)`
获取模拟输出状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `port` (int): 端口号
- `value` (float): 输出参数（需要初始化为浮点值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为输出状态

### 15.3 扩展I/O

#### `set_extend_digital_output(socketFd, port, value)`
设置扩展数字输出。

#### `get_extend_digital_input(socketFd, port, value)`
获取扩展数字输入。

#### `set_extend_analog_output(socketFd, port, value)`
设置扩展模拟输出。

#### `get_extend_analog_input(socketFd, port, value)`
获取扩展模拟输入。

## 16. 系统信息

### 16.1 控制器信息

#### `get_controller_id(socketFd, id)`
获取控制器ID。

#### `get_controller_id_csharp(socketFd, id)`
获取控制器ID（C#版本）。

**参数：**
- `id` (VectorChar): 输出ID字符数组

### 16.2 电机信息

#### `get_current_motor_torque(socketFd, motorTorque, motorTorqueSync)`
获取当前电机扭矩。

**参数：**
- `motorTorque` (VectorInt): 电机扭矩
- `motorTorqueSync` (VectorInt): 同步扭矩

#### `get_current_motor_speed(socketFd, motorSpeed, motorSpeedSync)`
获取当前电机转速。

### 16.3 传感器数据

#### `get_sensor_data(socketFd, sensorData)`
获取六轴力传感器数据。

**参数：**
- `sensorData` (VectorInt): 传感器数据数组

## 17. 程序控制

### 17.1 程序运行

#### `program_start(socketFd)`
启动程序。

#### `program_pause(socketFd)`
暂停程序。

#### `program_stop(socketFd)`
停止程序。

#### `program_continue(socketFd)`
继续程序。

#### `get_program_state(socketFd, state)`
获取程序状态。

**返回值：**
- `result[1]` (int): 程序状态

### 17.2 程序行控制

#### `get_program_current_line(socketFd, line)`
获取程序当前行号。

#### `set_program_current_line(socketFd, line)`
设置程序当前行号。

## 18. 作业管理

### 18.1 作业文件操作

#### `job_create(socketFd, jobName)`
创建新作业。

**参数：**
- `jobName` (str): 作业名称

#### `job_delete(socketFd, jobName)`
删除作业。

#### `job_upload_by_file(socketFd, filePath)`
通过文件上传作业。

**参数：**
- `filePath` (str): 文件路径

#### `job_download_by_directory(socketFd, directory, sync)`
下载作业到目录。

**参数：**
- `directory` (str): 目录路径
- `sync` (bool): 是否同步

#### `job_sync_job_file(socketFd)`
同步作业文件。

### 18.2 作业运行控制

#### `job_run(socketFd, jobName)`
运行作业。

#### `job_step(socketFd, jobName, line)`
单步运行作业。

**参数：**
- `line` (int): 行号

#### `job_pause(socketFd)`
暂停作业。

#### `job_continue(socketFd)`
继续作业。

#### `job_stop(socketFd)`
停止作业。

#### `job_run_times(socketFd, index)`
获取作业运行次数。

**参数：**
- `index` (int): 作业索引或计数器编号

**返回值：**
- `result[1]` (int): 运行次数

#### `job_break_point_run(socketFd, jobName)`
断点运行作业。

### 18.3 作业状态查询

#### `job_get_current_file(socketFd, jobName)`
获取当前作业文件名。

#### `job_get_current_file_csharp(socketFd, jobName)`
获取当前作业文件名（C#版本）。

#### `job_get_current_line(socketFd, line)`
获取当前作业行号。

**返回值：**
- `result[1]` (int): 当前行号

### 18.4 作业编辑

#### `job_insert_local_position(socketFd, posData)`
插入局部位置。

#### `job_set_local_position(socketFd, posName, posInfo)`
设置局部位置。

#### `job_insert_moveJ(socketFd, line, moveCmd)`
插入关节运动指令。

#### `job_insert_moveL(socketFd, line, moveCmd)`
插入直线运动指令。

#### `job_insert_moveC(socketFd, line, moveCmd)`
插入圆弧运动指令。

#### `job_insert_imove(socketFd, line, moveCmd)`
插入增量运动指令。

#### `job_insert_moveComm(socketFd, line, moveType, m_vel, m_acc, m_dec, m_time, m_pl)`
插入通用运动指令。

#### `job_insert_samov_command(socketFd, line, moveCmd, posData)`
插入SAMOV指令。

#### `job_insert_timer_command(socketFd, line, time)`
插入定时器指令。

#### `job_insert_io_out_command(socketFd, line, params)`
插入I/O输出指令。

### 18.5 作业逻辑控制

#### `job_insert_until(socketFd, line, conditionGroups, logic, logicGroup)`
插入UNTIL循环。

#### `job_insert_end_until(socketFd, line)`
插入END UNTIL。

#### `job_insert_while(socketFd, line, conditionGroups, logic, logicGroup)`
插入WHILE循环。

#### `job_insert_end_while(socketFd, line)`
插入END WHILE。

#### `job_insert_if(socketFd, line, conditionGroups, logic, logicGroup)`
插入IF条件。

#### `job_insert_end_if(socketFd, line)`
插入END IF。

#### `job_insert_label(socketFd, line, label)`
插入标签。

#### `job_insert_jump(socketFd, line, conditionGroups, logic, logicGroup, jumpConditionFlag, label)`
插入跳转指令。

### 18.6 视觉和传送带指令

#### `job_insert_vision_craft_start(socketFd, line, id)`
插入视觉工艺开始指令。

#### `job_insert_vision_craft_get_pos(socketFd, line, id, posName)`
插入视觉获取位置指令。

#### `job_insert_vision_craft_visual_trigger(socketFd, line, id)`
插入视觉触发指令。

#### `job_insert_vision_craft_visual_end(socketFd, line, id)`
插入视觉结束指令。

#### `job_insert_conveyor_check_pos(socketFd, line, id)`
插入传送带检查位置指令。

#### `job_insert_conveyor_check_end(socketFd, line, id)`
插入传送带检查结束指令。

#### `job_insert_conveyor_on(socketFd, line, id, postype, pos, vel, acc)`
插入传送带开启指令。

#### `job_insert_conveyor_off(socketFd, line, id)`
插入传送带关闭指令。

#### `job_insert_conveyor_pos(socketFd, line, id, posName)`
插入传送带位置指令。

#### `job_insert_conveyor_clear(socketFd, line, id, removeType)`
插入传送带清除指令。

#### `job_insert_cil(socketFd, line, moveCmd, id)`
插入CIL指令。

## 19. 队列运动控制

### 19.1 队列状态管理

#### `queue_motion_set_status(socketFd, status)`
设置队列运动状态。

#### `queue_motion_get_status(socketFd, status)`
获取队列运动状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `status` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为队列状态
- `result[1]` (int): 队列状态

**注意：**
- 循环调用时需要重置`status`参数为初始整数值

#### `queue_motion_clear_Data(socketFd)`
清除队列数据。

#### `queue_motion_size(socketFd, size)`
获取队列大小。

#### `queue_motion_get_queuelen(socketFd, len)`
获取队列长度。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `len` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为队列长度
- `result[1]` (int): 队列长度

**注意：**
- 循环调用时需要重置`len`参数为初始整数值

#### `queue_motion_send_to_controller(socketFd, size)`
发送队列到控制器。

### 19.2 队列运行控制

#### `queue_motion_suspend(socketFd)`
暂停队列运动。

#### `queue_motion_restart(socketFd)`
重启队列运动。

#### `queue_motion_stop(socketFd)`
停止队列运动。

#### `queue_motion_stop_not_power_off(socketFd)`
停止队列运动但不下电。

### 19.3 队列运动指令

#### `queue_motion_push_back_moveJ(socketFd, moveCmd)`
添加关节运动到队列。

#### `queue_motion_push_back_moveL(socketFd, moveCmd)`
添加直线运动到队列。

#### `queue_motion_push_back_moveC(socketFd, moveCmd)`
添加圆弧运动到队列。

#### `queue_motion_push_back_moveCA(socketFd, moveCmd)`
添加圆弧运动（角度）到队列。

#### `queue_motion_push_back_moveS(socketFd, moveCmd)`
添加样条运动到队列。

#### `queue_motion_push_back_moveJ_extra(socketFd, moveCmd)`
添加外部轴关节运动到队列。

#### `queue_motion_push_back_moveL_extra(socketFd, moveCmd)`
添加外部轴直线运动到队列。

#### `queue_motion_push_back_imove(socketFd, moveCmd)`
添加增量运动到队列。

#### `queue_motion_push_back_timer(socketFd, time)`
添加定时器到队列。

#### `queue_motion_push_back_dout(socketFd, port, value)`
添加数字输出到队列。

### 19.4 队列传送带和视觉指令

#### `queue_motion_push_back_conveyor_check_pos(socketFd, id)`
添加传送带检查位置到队列。

#### `queue_motion_push_back_conveyor_check_end(socketFd, id)`
添加传送带检查结束到队列。

#### `queue_motion_push_back_conveyor_on(socketFd, id, postype, pos, vel, acc)`
添加传送带开启到队列。

#### `queue_motion_push_back_conveyor_off(socketFd, id)`
添加传送带关闭到队列。

#### `queue_motion_push_back_conveyor_pos(socketFd, id, posName)`
添加传送带位置到队列。

#### `queue_motion_push_back_conveyor_clear(socketFd, id, removeType)`
添加传送带清除到队列。

#### `queue_motion_push_back_vision_craft_start(socketFd, id)`
添加视觉工艺开始到队列。

#### `queue_motion_push_back_vision_craft_get_pos(socketFd, id, posName)`
添加视觉获取位置到队列。

#### `queue_motion_push_back_vision_craft_visual_trigger(socketFd, id)`
添加视觉触发到队列。

#### `queue_motion_push_back_vision_craft_visual_end(socketFd, id)`
添加视觉结束到队列。

### 19.5 队列焊接指令

#### `queue_motion_push_back_arc_on(socketFd, id)`
添加弧焊开启到队列。

#### `queue_motion_push_back_arc_off(socketFd, id)`
添加弧焊关闭到队列。

#### `queue_motion_push_back_wave_on(socketFd, id)`
添加摆焊开启到队列。

#### `queue_motion_push_back_wave_off(socketFd, id)`
添加摆焊关闭到队列。

#### `queue_motion_push_back_tigweld_on(socketFd, type, l1, l2)`
添加TIG焊开启到队列。

#### `queue_motion_push_back_tigweld_off(socketFd)`
添加TIG焊关闭到队列。

#### `queue_motion_push_back_spot_weld(socketFd, id, time)`
添加点焊到队列。

### 19.6 队列逻辑控制

#### `queue_motion_push_back_until(socketFd, conditionGroups, logic, logicGroup)`
添加UNTIL循环到队列。

#### `queue_motion_push_back_end_until(socketFd)`
添加END UNTIL到队列。

#### `queue_motion_push_back_while(socketFd, conditionGroups, logic, logicGroup)`
添加WHILE循环到队列。

#### `queue_motion_push_back_end_while(socketFd)`
添加END WHILE到队列。

#### `queue_motion_push_back_if(socketFd, conditionGroups, logic, logicGroup)`
添加IF条件到队列。

#### `queue_motion_push_back_end_if(socketFd)`
添加END IF到队列。

#### `queue_motion_push_back_label(socketFd, label)`
添加标签到队列。

#### `queue_motion_push_back_jump(socketFd, conditionGroups, logic, logicGroup, jumpConditionFlag, label)`
添加跳转到队列。

#### `queue_motion_push_back_samov(socketFd, moveCmd, posData)`
添加SAMOV到队列。

#### `queue_motion_push_back_cil(socketFd, moveCmd, id)`
添加CIL到队列。

#### `queue_motion_push_back_TOFFSETON(socketFd, params)`
添加工具偏移开启到队列。

#### `queue_motion_push_back_TOFFSETOFF(socketFd)`
添加工具偏移关闭到队列。

## 20. 轨迹记录

### 20.1 轨迹记录控制

#### `track_record_start(socketFd, maxSamplingNum, samplingInterval)`
开始轨迹记录。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `maxSamplingNum` (double): 最大采样点数，范围[200, 12000]
- `samplingInterval` (double): 采样间隔（秒），范围[0.03, 1]

#### `track_record_stop(socketFd)`
停止轨迹记录。

**参数：**
- `socketFd` (int): 套接字文件描述符

#### `get_track_record_status(socketFd, recordStart)`
获取轨迹记录状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `recordStart` (bool): 输出参数（需要初始化为布尔值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为记录状态
- `result[1]` (bool): 记录状态（True=开启，False=关闭）

**注意：**
- 循环调用时需要重置`recordStart`参数

#### `track_record_save(socketFd, trajName)`
保存轨迹记录。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `trajName` (str): 轨迹保存名称

#### `track_record_playback(socketFd, vel)`
回放轨迹记录。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `vel` (int): 回放速度

#### `track_record_delete(socketFd)`
删除轨迹记录。

**参数：**
- `socketFd` (int): 套接字文件描述符

### 20.2 多机器人轨迹跟踪

所有轨迹跟踪函数都有对应的多机器人版本：

#### `track_record_start_robot(socketFd, robotNum, maxSamplingNum, samplingInterval)`
开始指定机器人的轨迹记录。

#### `track_record_stop_robot(socketFd, robotNum)`
停止指定机器人的轨迹记录。

#### `get_track_record_status_robot(socketFd, robotNum, recordStart)`
获取指定机器人的轨迹记录状态。

#### `track_record_save_robot(socketFd, robotNum, trajName)`
保存指定机器人的轨迹记录。

#### `track_record_playback_robot(socketFd, robotNum, vel)`
回放指定机器人的轨迹记录。

#### `track_record_delete_robot(socketFd, robotNum)`
删除指定机器人的轨迹记录。

**多机器人版本共同参数：**
- `robotNum` (int): 机器人编号

## 21. Modbus通信

### 21.1 Modbus主站

#### `modbus_set_master_parameter(socketFd, id, param)`
设置Modbus主站参数。

**参数：**
- `id` (int): 主站ID
- `param` (ModbusMasterParameter): 主站参数

#### `modbus_open_master(socketFd, id)`
打开Modbus主站。

#### `modbus_get_master_connection_status(socketFd, id, status)`
获取Modbus主站连接状态。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 主站ID
- `status` (int): 输出参数（需要初始化为整数值）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为连接状态
- `result[1]` (int): 连接状态

**注意：**
- 循环调用时需要重置`status`参数为初始整数值

### 21.2 Modbus读操作

#### `modbus_read_coil_status(socketFd, id, address, quantity, data)`
读取线圈状态（功能码01H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `quantity` (int): 数量
- `data` (VectorInt): 输出数据（需要预先初始化）

**返回值：**
- `result` (list): 返回列表，`result[0]`为错误码，`result[1]`为读取的数据

#### `modbus_read_input_status(socketFd, id, address, quantity, data)`
读取输入状态（功能码02H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `quantity` (int): 数量
- `data` (VectorInt): 输出数据（需要预先初始化）

#### `modbus_read_holding_registers(socketFd, id, address, quantity, data)`
读取保持寄存器（功能码03H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `quantity` (int): 数量
- `data` (VectorInt): 输出数据（需要预先初始化）

#### `modbus_read_input_registers(socketFd, id, address, quantity, data)`
读取输入寄存器（功能码04H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `quantity` (int): 数量
- `data` (VectorInt): 输出数据（需要预先初始化）

### 21.3 Modbus写操作

#### `modbus_write_signal_coil_status(socketFd, id, address, data)`
写单个线圈（功能码05H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 线圈地址
- `data` (int): 写入数据（0或1）

#### `modbus_write_signal_holding_registers(socketFd, id, address, data)`
写单个保持寄存器（功能码06H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 寄存器地址
- `data` (int): 写入数据

#### `modbus_write_multiple_coil_status(socketFd, id, address, data)`
写多个线圈（功能码0FH）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `data` (VectorInt): 写入数据列表

#### `modbus_write_multiple_holding_registers(socketFd, id, address, data)`
写多个保持寄存器（功能码10H）。

**参数：**
- `socketFd` (int): 套接字文件描述符
- `id` (int): 从站ID
- `address` (int): 起始地址
- `data` (VectorInt): 写入数据列表

## 22. 传送带跟踪

### 22.1 基本参数设置

#### `conveyor_belt_tracking_set_basic_parameter(socketFd, encoderVal, time, encoderDirection, encoderResolution, maxEncoderVal, minEncoderVal, posRecordMode, speed, userCoord, conveyorID, height, trackOnRunModeWithTargetOverrun, compensationEncoderVal)`
设置传送带跟踪基本参数。

**参数：**
- `encoderVal` (int): 编码器值
- `time` (int): 时间
- `encoderDirection` (int): 编码器方向
- `encoderResolution` (float): 编码器分辨率
- `maxEncoderVal` (int): 最大编码器值
- `minEncoderVal` (int): 最小编码器值
- `posRecordMode` (int): 位置记录模式
- `speed` (float): 速度
- `userCoord` (int): 用户坐标
- `conveyorID` (int): 传送带ID
- `height` (float): 高度
- `trackOnRunModeWithTargetOverrun` (bool): 运行模式跟踪
- `compensationEncoderVal` (int): 补偿编码器值

#### `conveyor_belt_tracking_set_identification_parameter(socketFd, conveyorID, detectSrcType, capturePos, visionID, visionIoFilterType, visionLatchEncoderValueType, communication, sensorTrg, type)`
设置传送带识别参数。

#### `conveyor_belt_tracking_set_sensor_calibration(socketFd, conveyorID, sensorPos)`
设置传送带传感器标定。

#### `conveyor_belt_tracking_set_tracking_range(socketFd, conveyorID, receLatestPos, trackRangeXMax, trackRangeYMax, trackRangeYMin, trackRangeZMax, trackRangeZMin, trackStartXPoint)`
设置传送带跟踪范围。

#### `conveyor_belt_tracking_set_tracking_wait_point(socketFd, conveyorID, isWait, delayDetectTime, pos)`
设置传送带跟踪等待点。

### 22.2 传送带标定

#### `conveyor_belt_set_sensor_calibration(socketFd, conveyorID)`
设置传感器标定。

#### `conveyor_belt_calibrate_for_sensor_point(socketFd, conveyorID)`
传感器点标定。

#### `conveyor_belt_calculate_for_sensor_point(socketFd, conveyorID)`
传感器点计算。

### 22.3 参数获取

#### `conveyor_belt_get_basic_paramters(socketFd, conveyorID, param)`
获取基本参数。

#### `conveyor_belt_get_identification_paramters(socketFd, conveyorID, param)`
获取识别参数。

#### `conveyor_belt_get_sensor_paramters(socketFd, conveyorID, param)`
获取传感器参数。

#### `conveyor_belt_get_track_range_paramters(socketFd, conveyorID, param)`
获取跟踪范围参数。

#### `conveyor_belt_get_wait_point_paramters(socketFd, conveyorID, param)`
获取等待点参数。

## 23. 激光切割

### 23.1 激光切割参数

#### `laser_cutting_set_global_parameter(socketFd, param)`
设置激光切割全局参数。

#### `laser_cutting_get_global_parameter(socketFd, param)`
获取激光切割全局参数。

#### `laser_cutting_set_craft_parameter(socketFd, param)`
设置激光切割工艺参数。

#### `laser_cutting_get_craft_parameter(socketFd, param)`
获取激光切割工艺参数。

#### `laser_cutting_set_analog_parameter(socketFd, param)`
设置激光切割模拟参数。

#### `laser_cutting_get_analog_parameter(socketFd, param)`
获取激光切割模拟参数。

#### `laser_cutting_set_io_parameter(socketFd, param)`
设置激光切割I/O参数。

#### `laser_cutting_get_io_parameter(socketFd, param)`
获取激光切割I/O参数。

## 24. 视觉系统

### 24.1 视觉基本参数

#### `vision_set_basic_parameter(socketFd, visionNum, vsPamrm)`
设置视觉基本参数。

**参数：**
- `visionNum` (int): 视觉编号
- `vsPamrm` (VisionParameter): 视觉参数

#### `vision_get_basic_parameter(socketFd, visionNum, vsPamrm)`
获取视觉基本参数。

#### `vision_set_range(socketFd, visionNum, vsPamrm)`
设置视觉范围。

#### `vision_get_range(socketFd, visionNum, vsPamrm)`
获取视觉范围。

#### `vision_set_position_parameter(socketFd, visionNum, vsPamrm)`
设置视觉位置参数。

#### `vision_get_position_parameter(socketFd, visionId, vsPamrm)`
获取视觉位置参数。

### 24.2 视觉标定

#### `vision_calibrate(socketFd, visionId, vsPamrm)`
视觉标定。

#### `vision_get_calibrate_data(socketFd, visionId, vsPamrm)`
获取视觉标定数据。

#### `vision_hand_eye_calibration_calculation(socketFd, visionNum)`
手眼标定计算。

## 25. 焊接功能

### 25.1 焊接配置

#### `weld_get_config(socketFd, index, param)`
获取焊接配置。

**参数：**
- `index` (int): 配置索引
- `param` (WeldParameter): 焊接参数

#### `weld_set_config(socketFd, index, param)`
设置焊接配置。

### 25.2 焊接控制

#### `weld_set_feed_wire(socketFd, state)`
设置送丝状态。

**参数：**
- `state` (bool): 送丝状态

#### `weld_set_rewind_wire(socketFd, state)`
设置收丝状态。

#### `weld_set_supply_gas(socketFd, state)`
设置供气状态。

#### `weld_set_enable(socketFd, state)`
设置焊接使能。

#### `weld_set_hand_spot(socketFd, state)`
设置手动点焊。

### 25.3 焊接状态

#### `weld_get_feed_wire_status(socketFd, status)`
获取送丝状态。

**返回值：**
- `result[1]` (bool): 送丝状态

#### `weld_get_wave_weld_param(socketFd, num, param)`
获取摆焊参数。

#### `weld_set_wave_weld_param(socketFd, num, param)`
设置摆焊参数。

#### `weld_get_monitor_status(socketFd, status)`
获取焊接监控状态。

## 26. 数据结构类

### 26.1 向量类

#### `VectorDouble()`
双精度浮点数向量。

**方法：**
- `append(value)`: 添加元素
- `size()`: 获取大小
- `[index]`: 访问元素

#### `VectorInt()`
整数向量。

#### `VectorString()`
字符串向量。

#### `VectorChar()`
字符向量。

### 26.2 运动命令类

#### `MoveCmd()`
运动命令对象。

**属性：**
- `targetPosType` (int): 目标位置类型
- `targetPosValue` (VectorDouble): 目标位置值
- `coord` (int): 坐标系
- `velocity` (int): 速度百分比
- `acc` (int): 加速度百分比
- `dec` (int): 减速度百分比
- `pl` (int): 平滑度等级
- `time` (float): 时间限制
- `toolNum` (int): 工具编号
- `userNum` (int): 用户坐标编号

**⚠️ 重要：`targetPosType` 参数详细说明**

`targetPosType` 是一个关键参数，它告诉控制器如何解释 `targetPosValue` 中的数据。**错误的设置会导致指令被忽略或执行失败**。

**正确的取值：**
- `0` 或 `nrc.PosType_data`: **数据类型** - `targetPosValue` 包含实际的坐标数组
- `1` 或 `nrc.PosType_PType`: **位置变量类型** - `targetPosValue` 包含预定义位置点的编号

**常见错误示例：**
```python
# ❌ 错误：使用位置变量类型但提供实际坐标
move_cmd.targetPosType = 1  # 告诉控制器期望位置变量编号
move_cmd.targetPosValue = nrc.VectorDouble()
for val in [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]:  # 但提供实际坐标
    move_cmd.targetPosValue.append(val)
# 结果：控制器无法解析，指令被忽略

# ✅ 正确：使用数据类型并提供实际坐标
move_cmd.targetPosType = nrc.PosType_data  # 或者写 0
move_cmd.targetPosValue = nrc.VectorDouble()
for val in [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]:  # 提供实际坐标
    move_cmd.targetPosValue.append(val)
# 结果：控制器正确解析并执行运动

# ✅ 正确：使用位置变量类型并提供位置编号
move_cmd.targetPosType = nrc.PosType_PType  # 或者写 1
move_cmd.targetPosValue = nrc.VectorDouble()
move_cmd.targetPosValue.append(1)  # 使用预定义的位置点P[1]
# 结果：控制器查找P[1]位置并执行运动
```

**使用建议：**
- **动态路径（如G-code执行）**：使用 `nrc.PosType_data`，因为坐标是实时计算的
- **固定位置点**：使用 `nrc.PosType_PType`，引用预先示教的位置点

#### `ServoMove()`
伺服运动对象。

**属性：**
- `targetPosType` (int): 目标位置类型
- `targetPosValue` (VectorDouble): 目标位置值
- `coord` (int): 坐标系
- `velocity` (int): 速度
- `userNum` (int): 用户坐标编号

### 26.3 参数类

#### `ToolParam()`
工具参数对象。

**属性：**
- `x`, `y`, `z` (float): 位置偏移
- `a`, `b`, `c` (float): 姿态偏移

#### `ModbusMasterParameter()`
Modbus主站参数。

#### `VisionParameter()`
视觉参数。

#### `WeldParameter()`
焊接参数。

## 27. 使用示例

### 27.1 基本连接和运动示例

```python
import nrc_interface as nrc
import time

# 连接机器人
socket_fd = nrc.connect_robot("192.168.1.100", "8080")
if socket_fd <= 0:
    print("连接失败")
    exit()

try:
    # 清除错误并上电
    nrc.clear_error(socket_fd)
    nrc.set_servo_state(socket_fd, 1)  # 就绪
    time.sleep(0.5)
    nrc.set_servo_poweron(socket_fd)   # 上电
    time.sleep(2)

    # 设置参数
    nrc.set_speed(socket_fd, 50)
    nrc.set_current_coord(socket_fd, COORD_USER)  # 用户坐标
    nrc.set_user_coord_number(socket_fd, 1)

    # 创建运动命令
    move_cmd = nrc.MoveCmd()
    move_cmd.targetPosType = nrc.PosType_data  # 使用实际坐标数据
    move_cmd.targetPosValue = nrc.VectorDouble()

    # 设置目标位置
    for pos in [10.0, 10.0, 20.0, 0.0, 0.0, 0.0]:
        move_cmd.targetPosValue.append(pos)

    move_cmd.coord = 3
    move_cmd.velocity = 50
    move_cmd.acc = 40
    move_cmd.dec = 40
    move_cmd.userNum = 1

    # 执行运动
    result = nrc.robot_movel(socket_fd, move_cmd)
    if result == 0:
        print("运动命令发送成功")

        # 等待运动完成
        while True:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                if result[1] == 0:  # 运动完成
                    break
            time.sleep(0.1)

        print("运动完成")

    # 下电
    nrc.set_servo_poweroff(socket_fd)

finally:
    # 断开连接
    nrc.disconnect_robot(socket_fd)
```

### 27.2 I/O控制示例

```python
# 设置数字输出
nrc.set_digital_output(socket_fd, 1, 1)  # 端口1输出高电平

# 读取数字输入
input_value = 0
result = nrc.get_digital_input(socket_fd, 1, input_value)
if isinstance(result, list) and len(result) > 1:
    actual_value = result[1]
    print(f"数字输入1的值: {actual_value}")

# 设置模拟输出
nrc.set_analog_output(socket_fd, 1, 5.0)  # 端口1输出5V

# 读取模拟输入
analog_value = 0.0
result = nrc.get_analog_input(socket_fd, 1, analog_value)
if isinstance(result, list) and len(result) > 1:
    actual_value = result[1]
    print(f"模拟输入1的值: {actual_value}V")
```

### 27.3 全局变量操作示例

```python
# 设置全局变量
nrc.set_global_variant(socket_fd, "GI001", 100.0)  # 整数变量
nrc.set_global_variant(socket_fd, "GD001", 25.5)   # 双精度变量
nrc.set_global_variant(socket_fd, "GB001", 1.0)    # 布尔变量

# 读取全局变量
value = 0.0
result = nrc.get_global_variant(socket_fd, "GI001", value)
if isinstance(result, list) and len(result) > 1:
    actual_value = result[1]
    print(f"GI001的值: {actual_value}")
```

## 28. 注意事项

### 28.1 返回值处理

**重要：** 对于带有引用(&)输出参数的C++函数：
- Python中返回列表
- 实际数据在 `result[1]` 中
- 第一个元素 `result[0]` 通常是错误代码

### 28.2 参数重置

在循环中调用函数时，必须重置输入参数：
```python
for i in range(10):
    status = 0  # 重置参数
    result = nrc.get_servo_state(socket_fd, status)
    # 处理结果...
```

### 28.3 多机器人支持

所有函数都有对应的 `_robot` 版本：
```python
# 单机器人
nrc.get_current_position(socket_fd, coord, pos)

# 多机器人（robotNum指定机器人编号）
nrc.get_current_position_robot(socket_fd, robotNum, coord, pos)
```

### 28.4 健壮的错误处理流程

#### 基本错误处理模式

```python
try:
    result = nrc.some_function(socket_fd, param)
    if isinstance(result, list) and len(result) > 1:
        actual_value = result[1]
    elif isinstance(result, int) and result != 0:
        print(f"函数调用失败，错误代码: {result}")
except Exception as e:
    print(f"异常: {e}")
```

#### 生产级错误处理流程

```python
def robust_robot_operation(socket_fd):
    """健壮的机器人操作示例"""

    # 1. 主动检查 - 执行前检查机器人状态
    def check_robot_status():
        try:
            servo_status = 0
            result = nrc.get_servo_state(socket_fd, servo_status)
            if isinstance(result, list) and len(result) > 1:
                status = result[1]
                if status == SERVO_ALARM:
                    print("⚠️ 机器人处于报警状态，尝试恢复...")
                    return recover_from_error()
                elif status != SERVO_RUNNING:
                    print(f"⚠️ 机器人状态异常: {status}")
                    return False
            return True
        except Exception as e:
            print(f"❌ 状态检查失败: {e}")
            return False

    # 2. 错误恢复流程
    def recover_from_error():
        try:
            # 获取具体错误信息
            error_code = 0
            result = nrc.get_robot_error_code(socket_fd, error_code)
            if isinstance(result, list) and len(result) > 1:
                print(f"错误代码: {result[1]}")

            # 清除错误
            nrc.clear_error(socket_fd)
            time.sleep(1)

            # 重新上电流程
            nrc.set_servo_state(socket_fd, SERVO_READY)
            time.sleep(0.5)
            nrc.set_servo_poweron(socket_fd)
            time.sleep(2)

            print("✅ 错误恢复成功")
            return True

        except Exception as e:
            print(f"❌ 错误恢复失败: {e}")
            return False

    # 3. 执行操作 - 带重试机制
    def execute_with_retry(operation, max_retries=3):
        for attempt in range(max_retries):
            try:
                if not check_robot_status():
                    continue

                result = operation()
                if result:
                    return True

            except Exception as e:
                print(f"❌ 操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                    continue

        return False

    # 4. 具体的运动操作
    def move_operation():
        move_cmd = nrc.MoveCmd()
        # ... 设置运动参数 ...

        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"运动命令失败: {result}")
            return False

        # 等待运动完成，带超时检查
        start_time = time.time()
        timeout = 30  # 30秒超时

        while time.time() - start_time < timeout:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                if result[1] == ROBOT_STOPPED:
                    return True
                elif result[1] == ROBOT_RUNNING:
                    time.sleep(0.1)
                    continue

            # 检查是否进入报警状态
            if not check_robot_status():
                return False

        print("❌ 运动超时")
        return False

    # 执行操作
    return execute_with_retry(move_operation)

# 使用示例
if robust_robot_operation(socket_fd):
    print("✅ 操作成功完成")
else:
    print("❌ 操作失败")
```

#### 错误代码参考表

以下是常见错误代码的详细说明和处理建议：

| 错误代码 | 含义 | 可能原因 | 处理方法 |
|:---|:---|:---|:---|
| 0 | 成功 | - | 继续执行 |
| 1 | 操作不允许 | 机器人状态不正确、模式错误 | 检查机器人状态和模式是否正确（如：是否上电？是否在自动模式？） |
| 2 | 参数错误 | 传入参数类型、范围或格式错误 | 检查函数传入的参数类型、范围、格式是否正确 |
| 3 | 连接断开 | 网络中断、控制器重启 | 检查网络连接，尝试重新连接机器人 |
| 4 | 接收失败 | 网络延迟、数据包丢失 | 检查网络连接和控制器状态，可尝试重发 |
| 5 | 位置不可达 | 目标位置超出工作空间、存在奇异点 | 检查目标位置是否在机器人工作空间内，或是否存在奇异点 |
| 6 | 运动中断 | 急停触发、碰撞检测、外部中断 | 检查是否有急停信号、碰撞检测触发或其它外部中断 |
| 7 | 超时错误 | 操作执行时间过长 | 检查网络延迟，增加超时时间或优化操作 |
| 8 | 权限不足 | 用户权限不够、安全锁定 | 检查用户权限设置和安全锁定状态 |
| 9 | 资源忙碌 | 系统正在执行其他任务 | 等待当前任务完成或停止当前任务 |
| 10 | 硬件故障 | 电机故障、传感器异常 | 检查硬件状态，可能需要维护 |

#### 错误诊断流程

当遇到错误时，建议按以下流程进行诊断：

```python
def diagnose_error(socket_fd, error_code):
    """错误诊断辅助函数"""
    print(f"🔍 诊断错误代码: {error_code}")

    if error_code == 1:  # 操作不允许
        # 检查伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            status = result[1]
            status_names = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            print(f"   伺服状态: {status_names.get(status, '未知')} ({status})")

            if status == SERVO_ALARM:
                print("   ⚠️ 机器人处于报警状态，需要清除错误")
                return "clear_error"
            elif status != SERVO_RUNNING:
                print("   ⚠️ 机器人未上电，需要执行上电流程")
                return "power_on"

        # 检查模式
        mode = 0
        result = nrc.get_current_mode(socket_fd, mode)
        if isinstance(result, list) and len(result) > 1:
            current_mode = result[1]
            mode_names = {0: "示教", 1: "远程", 2: "运行"}
            print(f"   当前模式: {mode_names.get(current_mode, '未知')} ({current_mode})")

            if current_mode != MODE_REMOTE:
                print("   ⚠️ 需要切换到远程模式")
                return "set_remote_mode"

    elif error_code == 3:  # 连接断开
        print("   🔌 网络连接问题")
        status = nrc.get_connection_status(socket_fd)
        print(f"   连接状态: {status}")
        return "reconnect"

    elif error_code == 5:  # 位置不可达
        print("   📍 位置可达性问题")
        print("   建议检查:")
        print("     • 目标位置是否在工作空间内")
        print("     • 是否存在关节限位")
        print("     • 是否存在奇异点")
        return "check_position"

    return "unknown"

def auto_recover_error(socket_fd, recovery_action):
    """自动错误恢复"""
    try:
        if recovery_action == "clear_error":
            print("🔧 执行错误清除...")
            nrc.clear_error(socket_fd)
            time.sleep(1)
            return True

        elif recovery_action == "power_on":
            print("🔋 执行上电流程...")
            nrc.clear_error(socket_fd)
            nrc.set_servo_state(socket_fd, SERVO_READY)
            time.sleep(0.5)
            nrc.set_servo_poweron(socket_fd)
            time.sleep(2)
            return True

        elif recovery_action == "set_remote_mode":
            print("🎮 切换到远程模式...")
            nrc.set_current_mode(socket_fd, MODE_REMOTE)
            time.sleep(0.5)
            return True

        elif recovery_action == "reconnect":
            print("🔄 尝试重新连接...")
            # 注意：这里需要重新获取socket_fd
            return False  # 需要上层处理重连

    except Exception as e:
        print(f"❌ 自动恢复失败: {e}")
        return False

    return False
```

---

## 29. 开发建议

### 29.1 常见错误避免指南

#### ⚠️ 关键错误：`targetPosType` 参数误用

这是导致"程序运行正常但机器人不动"的最常见原因：

**错误现象：**
- API调用都返回成功（错误码0）
- 程序没有报错或异常
- 机器人完全不动或只移动到零点位置
- 队列显示"执行完成"但实际没有按预期路径运动

**根本原因：**
```python
# ❌ 典型错误代码
move_cmd = nrc.MoveCmd()
move_cmd.targetPosType = 1  # 错误！告诉控制器期望位置变量编号
move_cmd.targetPosValue = nrc.VectorDouble()
for val in [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]:  # 但提供了实际坐标
    move_cmd.targetPosValue.append(val)
# 控制器无法解析，静默忽略指令
```

**正确做法：**
```python
# ✅ 正确代码
move_cmd = nrc.MoveCmd()
move_cmd.targetPosType = nrc.PosType_data  # 正确！告诉控制器这是实际坐标
move_cmd.targetPosValue = nrc.VectorDouble()
for val in [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]:  # 提供实际坐标
    move_cmd.targetPosValue.append(val)
# 控制器正确解析并执行
```

**调试技巧：**
1. 检查 `targetPosType` 与 `targetPosValue` 内容是否匹配
2. 使用简单的测试位置验证参数设置
3. 对比成功运行的示例代码

### 29.2 架构设计建议

#### 控制模式选型指南

根据应用场景选择最适合的控制模式：

```python
# 1. 直接指令模式 - 适用于简单脚本和测试
def simple_test_movement():
    nrc.robot_movel(socket_fd, move_cmd)  # 发送一条，执行一条
    # 优点：简单直接，适合学习和测试
    # 缺点：复杂路径需要大量循环，运动不够平滑

# 2. 作业模式 - 适用于生产环境的重复任务
def production_task():
    nrc.job_create(socket_fd, "production_job")
    nrc.job_insert_moveL(socket_fd, 1, move_cmd1)
    nrc.job_insert_moveL(socket_fd, 2, move_cmd2)
    nrc.job_run(socket_fd, "production_job")
    # 优点：稳定可靠，不依赖PC
    # 缺点：灵活性差，难以动态调整

# 3. 队列模式 - 适用于复杂动态应用
def dynamic_vision_task():
    nrc.queue_motion_set_status(socket_fd, True)
    for target in vision_targets:  # 动态目标
        move_cmd = create_move_cmd(target)
        nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
    # 优点：兼具灵活性和平滑性
    # 缺点：编程复杂度最高
```

#### 代码组织建议

```python
# 推荐的项目结构
class RobotController:
    def __init__(self, ip="************", port="6001"):
        self.socket_fd = None
        self.ip = ip
        self.port = port
        # 预分配常用数据结构
        self.pos_vector = nrc.VectorDouble()
        for i in range(7):
            self.pos_vector.append(0.0)

    def connect(self):
        """连接并初始化机器人"""
        self.socket_fd = nrc.connect_robot(self.ip, self.port)
        if self.socket_fd <= 0:
            raise ConnectionError("机器人连接失败")
        return self._initialize()

    def _initialize(self):
        """标准初始化流程"""
        nrc.clear_error(self.socket_fd)
        nrc.set_servo_state(self.socket_fd, SERVO_READY)
        time.sleep(0.5)
        nrc.set_servo_poweron(self.socket_fd)
        time.sleep(2)
        return True
```

#### 使用常量提高可读性

```python
# ✅ 推荐：使用常量
nrc.set_current_coord(socket_fd, COORD_USER)
nrc.set_servo_state(socket_fd, SERVO_READY)
nrc.set_current_mode(socket_fd, MODE_REMOTE)

# ❌ 不推荐：使用魔法数字
nrc.set_current_coord(socket_fd, 3)
nrc.set_servo_state(socket_fd, 1)
nrc.set_current_mode(socket_fd, 1)
```

### 29.2 性能优化建议

#### 数据结构优化

```python
class OptimizedRobotController:
    def __init__(self):
        # 预分配向量，避免频繁创建
        self.pos_vector = nrc.VectorDouble()
        self.joint_vector = nrc.VectorDouble()
        for i in range(7):
            self.pos_vector.append(0.0)
            self.joint_vector.append(0.0)

        # 预创建运动命令对象
        self.move_cmd = nrc.MoveCmd()
        self.move_cmd.targetPosValue = nrc.VectorDouble()
        for i in range(6):
            self.move_cmd.targetPosValue.append(0.0)

    def get_position_fast(self):
        """高效的位置获取"""
        # 重用预分配的向量
        result = nrc.get_current_position(self.socket_fd, COORD_CARTESIAN, self.pos_vector)
        if result == 0:
            return [self.pos_vector[i] for i in range(6)]
        return None
```

#### 批量操作优化

```python
def batch_setup(socket_fd):
    """批量设置，减少网络通信"""
    # 将相关设置组合在一起
    operations = [
        lambda: nrc.set_speed(socket_fd, 50),
        lambda: nrc.set_current_coord(socket_fd, COORD_USER),
        lambda: nrc.set_user_coord_number(socket_fd, 1),
        lambda: nrc.set_tool_hand_number(socket_fd, 1),
    ]

    for op in operations:
        op()
```

#### 队列模式性能优化

```python
def smooth_trajectory_execution(socket_fd, waypoints):
    """使用队列模式实现平滑轨迹"""
    # 启用队列模式
    nrc.queue_motion_set_status(socket_fd, True)
    nrc.queue_motion_clear_Data(socket_fd)

    # 批量添加路径点
    for point in waypoints:
        move_cmd = create_move_cmd(point)
        nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)

    # 一次性发送到控制器
    nrc.queue_motion_send_to_controller(socket_fd, len(waypoints))

    # 监控执行状态
    while True:
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            if result[1] == 0:  # 队列为空，执行完成
                break
        time.sleep(0.1)
```

### 29.3 安全编程实践

#### 完整的安全检查流程

```python
def safe_robot_operation(socket_fd, operation_func):
    """安全的机器人操作包装器"""

    # 1. 执行前安全检查
    def pre_operation_check():
        # 检查连接状态
        if nrc.get_connection_status(socket_fd) != 0:
            raise ConnectionError("机器人连接断开")

        # 检查伺服状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            if result[1] != SERVO_RUNNING:
                raise RuntimeError(f"机器人状态异常: {result[1]}")

        # 检查是否有运动中
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            if result[1] != ROBOT_STOPPED:
                print("⚠️ 机器人正在运动，等待停止...")
                wait_for_motion_complete(socket_fd)

    # 2. 执行操作
    try:
        pre_operation_check()
        return operation_func()

    except Exception as e:
        print(f"❌ 操作失败: {e}")
        # 3. 异常情况下的安全停止
        emergency_stop(socket_fd)
        raise

    finally:
        # 4. 后续安全检查
        post_operation_check(socket_fd)

def emergency_stop(socket_fd):
    """紧急停止流程"""
    try:
        nrc.robot_stop(socket_fd)  # 立即停止运动
        time.sleep(0.5)

        # 检查是否成功停止
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            if result[1] == ROBOT_STOPPED:
                print("✅ 机器人已安全停止")
            else:
                print("⚠️ 机器人可能未完全停止，请手动检查")

    except Exception as e:
        print(f"❌ 紧急停止失败: {e}")
        print("🚨 请立即手动检查机器人状态！")

def wait_for_motion_complete(socket_fd, timeout=30):
    """等待运动完成，带超时保护"""
    start_time = time.time()

    while time.time() - start_time < timeout:
        running_status = 0
        result = nrc.get_robot_running_state(socket_fd, running_status)
        if isinstance(result, list) and len(result) > 1:
            if result[1] == ROBOT_STOPPED:
                return True
            elif result[1] == ROBOT_RUNNING:
                time.sleep(0.1)
                continue

        # 检查是否进入异常状态
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            if result[1] == SERVO_ALARM:
                raise RuntimeError("机器人进入报警状态")

    raise TimeoutError(f"运动超时 ({timeout}秒)")
```

#### 资源管理最佳实践

```python
from contextlib import contextmanager

@contextmanager
def robot_session(ip="************", port="6001"):
    """机器人会话上下文管理器"""
    socket_fd = None
    try:
        # 连接和初始化
        socket_fd = nrc.connect_robot(ip, port)
        if socket_fd <= 0:
            raise ConnectionError("连接失败")

        # 标准初始化流程
        nrc.clear_error(socket_fd)
        nrc.set_servo_state(socket_fd, SERVO_READY)
        time.sleep(0.5)
        nrc.set_servo_poweron(socket_fd)
        time.sleep(2)

        yield socket_fd

    except Exception as e:
        print(f"❌ 会话异常: {e}")
        if socket_fd:
            emergency_stop(socket_fd)
        raise

    finally:
        # 确保资源清理
        if socket_fd:
            try:
                nrc.set_servo_poweroff(socket_fd)
                nrc.disconnect_robot(socket_fd)
                print("✅ 机器人会话已安全关闭")
            except:
                print("⚠️ 资源清理时发生错误")

# 使用示例
def main():
    with robot_session() as socket_fd:
        # 在这里执行机器人操作
        # 无论成功还是异常，都会自动清理资源
        pass
```

---

## 30. 函数名称验证与对照

### 30.1 重要函数名称确认

基于C++接口文档验证，以下是关键函数的正确名称：

#### 连接管理函数
- ✅ `connect_robot(ip, port)` - 连接机器人
- ✅ `disconnect_robot(socketFd)` - 断开连接
- ✅ `get_connection_status(socketFd)` - 获取连接状态

#### 伺服控制函数
- ✅ `get_servo_state(socketFd, status)` - 获取伺服状态
- ✅ `set_servo_state(socketFd, state)` - 设置伺服状态
- ✅ `set_servo_poweron(socketFd)` - 机器人上电
- ✅ `set_servo_poweroff(socketFd)` - 机器人下电

#### 位置获取函数
- ✅ `get_current_position(socketFd, coord, pos)` - 获取当前位置
- ✅ `get_current_extra_position(socketFd, pos)` - 获取外部轴位置
- ✅ `get_robot_running_state(socketFd, status)` - 获取运行状态

#### Modbus通信函数
- ✅ `modbus_set_master_parameter(socketFd, id, param)` - 设置主站参数
- ✅ `modbus_open_master(socketFd, id)` - 打开主站
- ✅ `modbus_get_master_connection_status(socketFd, id, status)` - 获取连接状态
- ✅ `modbus_read_coil_status(socketFd, id, address, quantity, data)` - 读取线圈状态
- ✅ `modbus_read_input_status(socketFd, id, address, quantity, data)` - 读取输入状态
- ✅ `modbus_read_holding_registers(socketFd, id, address, quantity, data)` - 读取保持寄存器
- ✅ `modbus_read_input_registers(socketFd, id, address, quantity, data)` - 读取输入寄存器
- ✅ `modbus_write_signal_coil_status(socketFd, id, address, data)` - 写单个线圈
- ✅ `modbus_write_signal_holding_registers(socketFd, id, address, data)` - 写单个寄存器
- ✅ `modbus_write_multiple_coil_status(socketFd, id, address, data)` - 写多个线圈
- ✅ `modbus_write_multiple_holding_registers(socketFd, id, address, data)` - 写多个寄存器

#### 轨迹跟踪函数
- ✅ `track_record_start(socketFd, maxSamplingNum, samplingInterval)` - 开始轨迹记录
- ✅ `track_record_stop(socketFd)` - 停止轨迹记录
- ✅ `get_track_record_status(socketFd, recordStart)` - 获取记录状态
- ✅ `track_record_save(socketFd, trajName)` - 保存轨迹
- ✅ `track_record_playback(socketFd, vel)` - 回放轨迹
- ✅ `track_record_delete(socketFd)` - 删除轨迹

### 30.2 多机器人版本函数

所有函数都有对应的`_robot`版本，例如：
- `get_servo_state_robot(socketFd, robotNum, status)`
- `get_current_position_robot(socketFd, robotNum, coord, pos)`
- `track_record_start_robot(socketFd, robotNum, maxSamplingNum, samplingInterval)`

### 30.3 参数类型验证

#### 重要参数类型说明
- `socketFd` - 始终为 `int` 类型
- `maxSamplingNum` - `double` 类型，范围 [200, 12000]
- `samplingInterval` - `double` 类型，范围 [0.03, 1] 秒
- `robotNum` - `int` 类型，机器人编号
- 所有引用参数在Python中都需要预先初始化

---

## 31. 文档信息

### 30.1 版本信息

**文档版本：** v2.1 - 完整验证版
**编写基础：** 基于 `lib\nrc_interface.py` 和完整的C++ API文档集合
**更新日期：** 2025年8月1日
**适用系统：** INEXBOT 3D打印机控制系统

**本次更新内容：**
- ✅ 验证了所有函数名称的正确性
- ✅ 修正了引用参数函数的返回值处理说明
- ✅ 添加了完整的C++接口文档链接
- ✅ 增强了Modbus通信函数的详细说明
- ✅ 完善了轨迹跟踪函数的参数类型和范围
- ✅ 添加了SWIG转换机制的技术说明
- ✅ 提供了函数名称验证和对照表

### 30.2 文档特色

本文档具有以下独特特色，使其成为一份真正的**开发指南**而非简单的API参考：

#### 🎯 **完整性**
- **483个函数** - 涵盖所有可用接口函数
- **28个功能模块** - 从基础连接到专业应用
- **准确的参数说明** - 基于官方C++文档，确保准确性

#### 🚀 **实用性**
- **编程模式对比** - 帮助选择最适合的控制方式
- **快速入门指南** - 可直接运行的Hello World示例
- **常量定义** - 提高代码可读性和可维护性
- **内联数据结构说明** - 减少文档间跳转

#### 🛡️ **健壮性**
- **完整的错误处理流程** - 包含诊断、恢复和重试机制
- **错误代码参考表** - 详细的故障排除指南
- **安全编程实践** - 生产级代码的安全保障
- **资源管理最佳实践** - 防止资源泄露

#### 📚 **教育性**
- **分步骤的详细示例** - 每个步骤都有清晰的说明
- **架构设计建议** - 帮助构建可维护的代码
- **性能优化技巧** - 提升应用程序效率
- **上下文和最佳实践** - 不仅告诉"是什么"，更解释"为什么"

### 30.3 使用建议

#### 对于初学者
1. 从**快速入门**章节开始
2. 理解**核心编程模式对比**
3. 使用**常量定义**编写代码
4. 参考**基本示例**进行练习

#### 对于有经验的开发者
1. 查阅**完整函数列表**作为参考
2. 采用**健壮的错误处理流程**
3. 应用**性能优化建议**
4. 实施**安全编程实践**

#### 对于生产环境
1. 必须实现**完整的错误处理**
2. 使用**资源管理上下文管理器**
3. 建立**错误诊断和恢复机制**
4. 定期参考**错误代码参考表**

### 30.4 反馈与支持

如果您在使用过程中遇到问题或有改进建议，请：

1. **检查错误代码参考表** - 大多数问题都有标准解决方案
2. **参考健壮性编程章节** - 了解如何处理异常情况
3. **查阅C++原始文档** - 获取更详细的技术规范
4. **使用诊断工具** - 利用文档中的错误诊断函数

### 30.5 致谢

本文档的编写得益于：
- **INEXBOT团队** 提供的优秀C++接口和文档
- **SWIG工具** 实现了C++到Python的无缝绑定

---

**📖 这不仅仅是一份API文档，更是一份完整的开发指南**

*本文档致力于帮助开发者从入门到精通，编写出高质量、高可靠性的机器人控制应用程序。无论您是初学者还是专家，都能在这里找到有价值的信息和指导。*

**🎉 祝您开发愉快！**
