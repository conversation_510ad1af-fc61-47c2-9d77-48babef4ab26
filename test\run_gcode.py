#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (队列模式版本)

功能:
1. 读取一个标准的G-code文件。
2. 解析其中的G1移动指令 (包括X, Y, Z位置和A, B, C角度)。
3. 连接到INEXBOT机械臂。
4. 使用队列模式批量发送路径点，实现平滑连续运动。
5. 在指定的用户坐标系下，将G-code路径点转换为机械臂的movel指令并执行。
6. 监控队列执行状态，确保所有运动完成。
7. 任务结束后安全下电并断开连接。

注意:
- 此版本使用队列模式，可以实现更平滑的连续运动
- 支持完整的6轴运动，包括XYZ位置和ABC角度
- 队列模式适合连续路径执行，减少运动间的停顿
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径 (请根据您的项目结构调整)
# 假设此脚本与您之前的测试脚本在同一目录下
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# 队列模式全局变量
queue_size = 0  # 队列中指令的数量

def normalize_angle_degrees(angle):
    """
    将角度标准化到 [-180, 180] 范围内
    这与机械臂内部的角度标准化保持一致
    """
    while angle > 180:
        angle -= 360
    while angle <= -180:
        angle += 360
    return angle


def get_current_pose_angles(socket_fd):
    """获取机械臂当前姿态角度（度）"""
    try:
        pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 3, pos)  # 用户坐标系（与运动命令一致）
        if result == 0 and len(pos) >= 6:
            # 将弧度转换为度并标准化
            rx_deg = normalize_angle_degrees(math.degrees(pos[3]))
            ry_deg = normalize_angle_degrees(math.degrees(pos[4]))
            rz_deg = normalize_angle_degrees(math.degrees(pos[5]))
            return [rx_deg, ry_deg, rz_deg]
        else:
            print(f"❌ 获取当前姿态失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前姿态时发生错误: {e}")
        return None


def setup_queue_mode(socket_fd):
    """设置队列模式"""
    global queue_size
    print("🔧 正在设置队列模式...")

    try:
        # 1. 设置远程模式
        result = nrc.set_current_mode(socket_fd, 1)  # 1 = 远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return False
        print("✅ 远程模式设置成功")
        time.sleep(0.5)

        # 2. 启动队列模式
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启动队列模式失败，错误码: {result}")
            return False
        print("✅ 队列模式启动成功")
        time.sleep(0.5)

        # 3. 清除队列数据
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return False
        print("✅ 队列数据清除成功")

        # 重置队列大小计数器
        queue_size = 0
        return True

    except Exception as e:
        print(f"❌ 设置队列模式时发生错误: {e}")
        return False


def setup_queue_mode_improved(socket_fd):
    """设置队列模式（改进版）- 更仔细的初始化和清理"""
    global queue_size

    try:
        print("🔧 设置队列模式（改进版）...")

        # 1. 首先确保队列模式是关闭的，清空任何残留队列
        print("   🧹 清空可能存在的残留队列...")
        result = nrc.queue_motion_set_status(socket_fd, False)
        if result != 0:
            print(f"   ⚠️ 关闭队列模式失败，错误码: {result}")
        else:
            print("   ✅ 队列模式已关闭，残留队列已清空")

        time.sleep(0.5)  # 等待清理完成

        # 2. 清空本地队列数据
        print("   🧹 清空本地队列数据...")
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"   ⚠️ 清空本地队列数据失败，错误码: {result}")
        else:
            print("   ✅ 本地队列数据已清空")

        # 3. 重置本地队列计数器
        queue_size = 0

        # 4. 重新启动队列模式
        print("   🚀 启动队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启动队列模式失败，错误码: {result}")
            return False

        print("✅ 队列模式设置成功")
        return True

    except Exception as e:
        print(f"❌ 设置队列模式时发生错误: {e}")
        return False


def cleanup_queue_mode(socket_fd):
    """清理队列模式"""
    print("🧹 正在清理队列模式...")

    try:
        # 1. 关闭队列模式
        result = nrc.queue_motion_set_status(socket_fd, False)
        if result != 0:
            print(f"⚠️ 关闭队列模式失败，错误码: {result}")
        else:
            print("✅ 队列模式已关闭")

        # 2. 设置回示教模式
        result = nrc.set_current_mode(socket_fd, 0)  # 0 = 示教模式
        if result != 0:
            print(f"⚠️ 设置示教模式失败，错误码: {result}")
        else:
            print("✅ 已切换回示教模式")

    except Exception as e:
        print(f"⚠️ 清理队列模式时发生错误: {e}")


def execute_single_move_with_queue(socket_fd, target_pos, velocity_percent, accel_percent, smoothing_level):
    """使用队列模式执行单个移动指令 - 逐个发送并执行"""
    try:
        # 1. 清空队列
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清空队列失败，错误码: {result}")
            return False

        # 2. 创建笛卡尔运动命令
        print(f"    🔄 执行单个队列运动...")
        print(f"       目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        print(f"       目标姿态: RX={math.degrees(target_pos[3]):.3f}°, RY={math.degrees(target_pos[4]):.3f}°, RZ={math.degrees(target_pos[5]):.3f}°")
        print(f"       坐标系: 用户坐标系{USER_COORD_NUMBER} (coord=3)")

        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = nrc.PosType_data  # 0=数据类型（实际坐标）
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)

        move_cmd.coord = 3  # 用户坐标系（与成功版本一致）
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = accel_percent
        move_cmd.dec = accel_percent
        move_cmd.pl = smoothing_level

        # 3. 添加到队列
        result = nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 添加到队列失败，错误码: {result}")
            return False

        # 4. 立即发送并执行（队列大小为1）
        result = nrc.queue_motion_send_to_controller(socket_fd, 1)
        if result != 0:
            print(f"❌ 发送队列失败，错误码: {result}")
            return False

        print(f"    ✅ 队列指令发送成功，等待执行...")
        return True

    except Exception as e:
        print(f"❌ 执行队列运动时发生错误: {e}")
        return False


def add_move_to_queue(socket_fd, target_pos, velocity_percent, accel_percent, smoothing_level):
    """添加移动指令到队列 - 使用笛卡尔坐标直线运动"""
    global queue_size

    try:
        # 直接使用笛卡尔坐标，不需要转换为关节角度
        print(f"    🔄 添加笛卡尔坐标到队列...")
        print(f"       目标位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
        print(f"       目标姿态: RX={math.degrees(target_pos[3]):.3f}°, RY={math.degrees(target_pos[4]):.3f}°, RZ={math.degrees(target_pos[5]):.3f}°")
        print(f"       坐标系: 用户坐标系{USER_COORD_NUMBER} (coord=3)")

        # 创建笛卡尔运动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = nrc.PosType_data  # 0=数据类型（实际坐标）
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)

        move_cmd.coord = 3  # 用户坐标系（与成功版本一致）
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = velocity_percent
        move_cmd.acc = accel_percent
        move_cmd.dec = accel_percent
        move_cmd.pl = smoothing_level

        # 使用直线运动队列
        result = nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
        if result == 0:
            queue_size += 1
            print(f"    ✅ 成功添加直线运动到队列，当前队列大小: {queue_size}")
            return True
        else:
            print(f"❌ 添加直线运动到队列失败，错误码: {result}")
            return False

    except Exception as e:
        print(f"❌ 添加指令到队列时发生错误: {e}")
        return False


def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待运动完成"""
    try:
        print("⏳ 等待运动完成...")
        start_time = time.time()

        while True:
            # 检查机器人运行状态
            status = 0
            result = nrc.get_robot_running_state(socket_fd, status)
            if isinstance(result, list) and len(result) > 1:
                status = result[1]
                if status == 0:  # 0=停止状态，表示运动完成
                    print("✅ 运动完成")
                    return True

            # 检查超时
            if time.time() - start_time > timeout_seconds:
                print(f"❌ 运动超时 ({timeout_seconds}秒)")
                return False

            time.sleep(0.1)  # 100ms检查间隔

    except Exception as e:
        print(f"❌ 等待运动完成时发生错误: {e}")
        return False


def send_queue_and_wait(socket_fd, timeout_seconds=300):
    """发送队列并等待执行完成"""
    global queue_size

    if queue_size == 0:
        print("⚠️ 队列为空，无需发送")
        return True

    try:
        print(f"📤 正在发送队列到控制器，队列大小: {queue_size}")

        # 发送队列到控制器
        result = nrc.queue_motion_send_to_controller(socket_fd, queue_size)
        if result != 0:
            print(f"❌ 发送队列失败，错误码: {result}")
            return False

        print("✅ 队列发送成功，开始执行...")
        time.sleep(0.5)

        # 检查队列长度确认发送成功
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
            print(f"📊 控制器队列长度: {queue_len}")

        # 重置本地队列计数器
        queue_size = 0

        # 等待队列执行完成
        print("⏳ 正在等待队列执行完成...", end="", flush=True)
        start_time = time.time()
        last_print_time = 0

        while time.time() - start_time < timeout_seconds:
            try:
                # 检查机器人运行状态
                running_status = 0
                result = nrc.get_robot_running_state(socket_fd, running_status)
                if isinstance(result, list) and len(result) > 1:
                    running_status = result[1]

                # 检查队列长度
                queue_len = 0
                result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
                if isinstance(result, list) and len(result) > 1:
                    queue_len = result[1]

                # 如果机器人停止运行且队列为空，说明执行完成
                if running_status == 0 and queue_len == 0:
                    print(" ✅")
                    print("🎉 队列执行完成！")
                    return True

                # 定期打印状态
                if time.time() - last_print_time > 2:
                    print(".", end="", flush=True)
                    last_print_time = time.time()

                time.sleep(0.1)

            except Exception as e:
                print(f"\n⚠️ 检查执行状态时发生错误: {e}")
                time.sleep(0.5)

        print(" ❌ 超时!")
        print(f"❌ 队列执行超时 ({timeout_seconds}秒)")
        return False

    except Exception as e:
        print(f"❌ 发送队列时发生错误: {e}")
        return False

# --- 全局参数配置 ---

# 1. 要执行的G-code文件
#    请将您想要执行的G-code文件放在此脚本的同级目录下
GCODE_FILE = "jiyi.Gcode"

# 2. 机械臂上对应的用户坐标系编号
#    这个编号必须与G-code路径所基于的坐标系严格对应
USER_COORD_NUMBER = 1

# 3. 运动参数
VELOCITY_PERCENT = 20  # 速度百分比 (0-100) - 降低速度便于观察
ACCEL_PERCENT = 20     # 加速度百分比 (0-100)
SMOOTHING_LEVEL = 0    # 平滑度 (0-8, 0表示精确定位)
TIMEOUT_SECONDS = 60   # 单步运动的超时时间 (秒)

# 4. 队列模式参数
QUEUE_BATCH_SIZE = 20  # 队列批次大小 (一次发送的指令数量，0表示一次发送所有指令)
QUEUE_TIMEOUT_MULTIPLIER = 2  # 队列超时倍数 (相对于单步超时时间)

# 5. G-code默认角度值 (当G-code中没有指定ABC角度时的G-code角度值)
# 这些是G-code坐标系中的默认角度，会加上偏移量后传递给机械臂
GCODE_DEFAULT_A = 0.0   # G-code中A轴的默认角度 (度)
GCODE_DEFAULT_B = 0.0   # G-code中B轴的默认角度 (度)
GCODE_DEFAULT_C = 0.0   # G-code中C轴的默认角度 (度)

# 6. G-code角度到机械臂角度的偏移量
# G-code中A=0°对应机械臂的180°，所以需要加上180°的偏移
GCODE_TO_ROBOT_OFFSET_A = 180.0  # A轴偏移量 (度)
GCODE_TO_ROBOT_OFFSET_B = 0.0    # B轴偏移量 (度)
GCODE_TO_ROBOT_OFFSET_C = 0  # C轴偏移量 (度)

# 7. 计算出的机械臂默认姿态 (G-code默认角度 + 偏移量)
DEFAULT_RX = GCODE_DEFAULT_A + GCODE_TO_ROBOT_OFFSET_A  # = 0 + 180 = 180°
DEFAULT_RY = GCODE_DEFAULT_B + GCODE_TO_ROBOT_OFFSET_B  # = 0 + 0 = 0°
DEFAULT_RZ = GCODE_DEFAULT_C + GCODE_TO_ROBOT_OFFSET_C  # = 0 +


def parse_gcode_file(filepath):
    """
    解析G-code文件，提取G1指令的路径点。
    返回一个包含XYZ位置和ABC角度的字典列表。
    """
    print(f"📄 正在解析G-code文件: {filepath}")
    path = []

    # 正则表达式用于匹配G0/G1指令中的坐标轴和值（包括XYZABC）
    coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip().upper()
                # 忽略注释和空行
                if not line or line.startswith(';'):
                    continue

                if line.startswith('G1') or line.startswith('G0'):
                    coords = dict(coord_regex.findall(line))
                    # 只有当至少包含XYZ坐标时才添加路径点
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A')) if 'A' in coords else None,
                            'b': float(coords.get('B')) if 'B' in coords else None,
                            'c': float(coords.get('C')) if 'C' in coords else None
                        }
                        path.append(point)
        
        print(f"✅ 解析完成，共找到 {len(path)} 个路径点。")
        return path
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None


# --- 从您提供的测试脚本中复用的核心函数 ---
# (为了代码完整性，这里复制了这些函数。您可以保持它们在外部lib中)

def wait_for_motion_complete(socket_fd, timeout_seconds=30):
    """等待机器人运动完成"""
    print("  ⏳ 正在等待机器人运动完成...", end="", flush=True)
    start_time = time.time()
    last_print_time = 0

    while time.time() - start_time < timeout_seconds:
        try:
            running_status = 0
            result = nrc.get_robot_running_state(socket_fd, running_status)
            if isinstance(result, list) and len(result) > 1:
                running_status = result[1]
            
            if running_status == 0:
                print(" ✅")
                return True
            
            if time.time() - last_print_time > 2: # 每2秒打印一次状态
                 print(".", end="", flush=True)
                 last_print_time = time.time()

            time.sleep(0.1)
        except Exception as e:
            print(f"\n  (检查运动状态时发生错误: {e})")
            time.sleep(0.5)

    print(" ❌ 超时!")
    return False

def robot_power_on_if_needed(socket_fd):
    """如果需要则执行上电操作"""
    try:
        servo_status = 0
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1:
            servo_status = result[1]
        
        if servo_status == 3:
            print("✅ 机器人伺服已上电。")
            return True
        
        print("ℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        if servo_status == 0:
            nrc.set_servo_state(socket_fd, 1)
            time.sleep(0.2)
        
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 上电失败！返回码: {result}。请检查安全回路、急停按钮等。")
            return False
        
        time.sleep(1.5)
        
        result = nrc.get_servo_state(socket_fd, servo_status)
        if isinstance(result, list) and len(result) > 1 and result[1] == 3:
            print("✅ 机器人上电成功！")
            return True
        else:
            print(f"❌ 上电后状态异常: {result}")
            return False
    except Exception as e:
        print(f"❌ 上电过程失败: {e}")
        return False

def robot_power_off(socket_fd):
    """机器人下电操作"""
    print("\nℹ️ 正在安全下电...")
    try:
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")
        return True
    except Exception as e:
        print(f"❌ 下电过程失败: {e}")
        return False

def get_current_pose(socket_fd):
    """获取机械臂当前位姿"""
    try:
        pos = nrc.VectorDouble()
        # 参数：socketFd, coord(坐标系类型), pos
        # coord: 0=关节，1=直角，2=工具，3=用户
        # 这里使用1=直角坐标系获取笛卡尔位置
        result = nrc.get_current_position(socket_fd, 1, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"❌ 获取当前位姿失败，错误码: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位姿时发生错误: {e}")
        return None


def execute_gcode_on_robot():
    """主执行函数 - 队列模式版本（改进版）"""
    print("=" * 60)
    print("INEXBOT机械臂 G-code 路径执行程序 (队列模式 - 改进版)")
    print("=" * 60)

    # 1. 解析G-code文件
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return

    socket_fd = -1
    try:
        # 2. 连接机械臂
        print(f"🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        if socket_fd <= 0:
            print("❌ 连接失败！")
            return
        print(f"✅ 连接成功！Socket ID: {socket_fd}")

        # 3. 检查并上电机器人
        if not robot_power_on_if_needed(socket_fd):
            return

        # 4. 设置运动所需的用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        time.sleep(0.2)

        # 5. 设置远程模式
        print("\n🔧 设置远程模式...")
        result = nrc.set_current_mode(socket_fd, 1)  # 1 = 远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return
        print("✅ 远程模式设置成功")

        # 6. 显示队列模式优势
        print("\n🎯 队列模式优势:")
        print("   ✅ 平滑连续运动，减少运动间停顿")
        print("   ✅ 提高执行效率，适合连续路径")
        print("   ✅ 减少通信开销，批量发送指令")
        print("   ✅ 更好的运动规划和轨迹优化")

        # 6. 显示角度处理配置
        print(f"\nℹ️ G-code默认角度: A={GCODE_DEFAULT_A:.1f}°, B={GCODE_DEFAULT_B:.1f}°, C={GCODE_DEFAULT_C:.1f}°")
        print(f"ℹ️ G-code角度偏移: A+{GCODE_TO_ROBOT_OFFSET_A:.1f}°, B+{GCODE_TO_ROBOT_OFFSET_B:.1f}°, C+{GCODE_TO_ROBOT_OFFSET_C:.1f}°")
        print(f"ℹ️ 机械臂默认姿态: RX={DEFAULT_RX:.1f}°, RY={DEFAULT_RY:.1f}°, RZ={DEFAULT_RZ:.1f}°")
        print(f"   转换为弧度: RX={math.radians(DEFAULT_RX):.5f}, RY={math.radians(DEFAULT_RY):.5f}, RZ={math.radians(DEFAULT_RZ):.5f}")
        print("   所有角度（G-code指定或默认）都会加上偏移量后传递给机械臂")

        # 7. 获取初始姿态作为参考
        print("\nℹ️ 获取当前机械臂姿态作为角度计算参考...")
        current_angles = get_current_pose_angles(socket_fd)
        if current_angles:
            print(f"   当前姿态: RX={current_angles[0]:.3f}°, RY={current_angles[1]:.3f}°, RZ={current_angles[2]:.3f}°")
        else:
            print("   无法获取当前姿态，将使用默认参考角度")
            current_angles = [DEFAULT_RX, DEFAULT_RY, DEFAULT_RZ]

        # 8. 设置队列模式（改进版）
        if not setup_queue_mode_improved(socket_fd):
            print("❌ 队列模式设置失败，程序终止")
            return

        # 9. 逐个执行路径点（队列模式）
        print("\n" + "=" * 40)
        print(f"🚀 使用队列模式逐个执行 {len(gcode_path)} 个路径点...")
        print("📦 执行方式: 逐个发送并执行（避免批量问题）")
        print("=" * 40)

        # 逐个执行路径点（队列模式）
        for i, point in enumerate(gcode_path):
            print(f"\n--- 移动到点 {i + 1}/{len(gcode_path)} ---")

            # 使用G-code中的XYZ位置和ABC角度（如果有的话）
            # 统一处理：无论是G-code指定的角度还是默认角度，都加上偏移量
            gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
            gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
            gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

            # 加上偏移量得到机械臂角度
            rx_deg_raw = gcode_a + GCODE_TO_ROBOT_OFFSET_A
            ry_deg_raw = gcode_b + GCODE_TO_ROBOT_OFFSET_B
            rz_deg_raw = gcode_c + GCODE_TO_ROBOT_OFFSET_C

            # 标准化角度
            rx_deg = normalize_angle_degrees(rx_deg_raw)
            ry_deg = normalize_angle_degrees(ry_deg_raw)
            rz_deg = normalize_angle_degrees(rz_deg_raw)

            # 将角度转换为弧度（API需要弧度）
            rx_rad = math.radians(rx_deg)
            ry_rad = math.radians(ry_deg)
            rz_rad = math.radians(rz_deg)

            target_pos = [point['x'], point['y'], point['z'], rx_rad, ry_rad, rz_rad]

            print(f"  位置: X={target_pos[0]:.3f}, Y={target_pos[1]:.3f}, Z={target_pos[2]:.3f}")
            print(f"  姿态: RX={rx_deg:.3f}°, RY={ry_deg:.3f}°, RZ={rz_deg:.3f}°")

            # 使用队列模式执行单个移动
            if not execute_single_move_with_queue(socket_fd, target_pos, VELOCITY_PERCENT, ACCEL_PERCENT, SMOOTHING_LEVEL):
                print(f"❌ 执行路径点 {i + 1} 运动失败，程序终止")
                return

            # 等待运动完成
            if not wait_for_motion_complete(socket_fd, timeout_seconds=TIMEOUT_SECONDS):
                print(f"❌ 路径点 {i + 1} 运动超时，程序终止")
                return

            print(f"✅ 路径点 {i + 1} 执行完成")

        print("\n" + "=" * 40)
        print("🎉 队列模式G-code路径执行完毕！")
        print("=" * 40)

    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        # 清理和断开连接
        if socket_fd > 0:
            # 清理队列模式
            cleanup_queue_mode(socket_fd)

            # 安全下电并断开连接
            robot_power_off(socket_fd)
            print("🔌 正在断开连接...")
            nrc.disconnect_robot(socket_fd)
            print("✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_on_robot()